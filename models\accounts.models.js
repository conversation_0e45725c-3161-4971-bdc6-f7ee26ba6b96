const pool = require("../config/db");
const bcrypt = require("bcryptjs");
const RoleType = require("../constants/dbConstants");

module.exports = class Accounts {
  static async fetchAccounts(userId) {
    try {
      const userData = await pool.query("SELECT * FROM users WHERE id = ?", [
        userId,
      ]);
      if (userData[0].roleId === RoleType.Admin) {
        const results = await pool.query("SELECT * FROM gmb_accounts");
        return results;
      }
      if (userData[0].roleId === RoleType.Manager) {
        const results = await pool.query(
          `SELECT 
    gbm.id AS businessId,
    ub.userId,
	acc.* FROM 
    gmb_businesses_master gbm
    JOIN user_business ub ON ub.businessId = gbm.id
    JOIN
    gmb_accounts acc ON 
    gbm.id = acc.businessId WHERE 
    ub.userId = ? AND ub.isGoogleSyncComplete = 1`,
          [userId]
        );
        return results;
      } else {
        const results = await pool.query(
          `SELECT DISTINCT
    u.id AS user_id,
    acc.* FROM 
    users u JOIN 
    users_gmb_locations ul ON u.id = ul.userId JOIN 
    gmb_locations loc ON ul.gmbLocationId = loc.gmbLocationId JOIN 
    gmb_accounts acc ON loc.gmbAccountId = acc.accountId WHERE 
    u.id = ?`,
          [userId]
        );
        return results;
      }
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }
};
