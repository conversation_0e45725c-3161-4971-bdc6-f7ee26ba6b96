require('dotenv').config();
const { checkAndModifyConstraints } = require('./migrations/check_and_modify_constraints');

/**
 * <PERSON><PERSON><PERSON> to run the database migration
 * Usage: node database/run_migration.js
 */

console.log("=".repeat(60));
console.log("GMB LOCATIONS TABLE CONSTRAINT MIGRATION");
console.log("=".repeat(60));
console.log("This script will modify the gmb_locations table to:");
console.log("- Remove unique constraint on gmbLocationId only");
console.log("- Add composite unique constraint on (gmbAccountId, gmbLocationId)");
console.log("=".repeat(60));

// Run the migration
checkAndModifyConstraints()
  .then(() => {
    console.log("\n" + "=".repeat(60));
    console.log("MIGRATION COMPLETED SUCCESSFULLY!");
    console.log("=".repeat(60));
  })
  .catch((error) => {
    console.error("\n" + "=".repeat(60));
    console.error("MIGRATION FAILED!");
    console.error("=".repeat(60));
    console.error("Error details:", error);
  });
