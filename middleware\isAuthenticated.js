const jwt = require("jsonwebtoken");

const isAuthenticated = (req, res, next) => {
  try {
    const token = req.headers["authentication-token"];
    // check for valid token
    const decoded = jwt.verify(token, process.env.APP_JWT_SECRET_KEY);
    if (token && decoded) {
      req["user"] = decoded;
      return next();
    } else {
      req["user"] = {};
      return res.status(401).json({ error: "Un-authorized" });
    }
  } catch (error) {
    req["user"] = {};
    console.log(`ERROR: IsAuthenticated Middleware Is Failing ${error}`);
    return res.status(401).json({ error: "Un-authorized" });
  }
};

module.exports = { isAuthenticated };
