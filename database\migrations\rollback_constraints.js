// Load environment variables from .env.development
require("dotenv").config({ path: ".env.development" });
const pool = require("../../config/db");

/**
 * Rollback script to revert gmb_locations table constraints
 * Reverts composite constraint back to gmbLocationId only unique constraint
 */

async function rollbackConstraints() {
  try {
    console.log("Starting rollback: Revert gmb_locations unique constraint");

    // Step 1: Check current table structure
    console.log("1. Checking current table structure...");
    const indexes = await pool.query("SHOW INDEX FROM gmb_locations");
    console.log("Current indexes:", indexes);

    // Step 2: Find and drop composite constraint
    console.log("2. Looking for composite constraint to remove...");
    const compositeConstraints = indexes.filter(
      (index) =>
        index.Key_name === "UK_gmb_locations_account_location" ||
        (index.Column_name === "gmbAccountId" && index.Non_unique === 0)
    );

    if (compositeConstraints.length > 0) {
      // Get unique constraint names
      const constraintNames = [
        ...new Set(compositeConstraints.map((c) => c.Key_name)),
      ];

      for (const constraintName of constraintNames) {
        console.log(`   Dropping composite constraint: ${constraintName}`);
        await pool.query(
          `ALTER TABLE gmb_locations DROP INDEX ${constraintName}`
        );
        console.log(`   ✓ Dropped constraint: ${constraintName}`);
      }
    } else {
      console.log("   No composite constraint found to remove");
    }

    // Step 3: Check if gmbLocationId unique constraint exists
    console.log("3. Checking if gmbLocationId unique constraint exists...");
    const gmbLocationIdConstraints = indexes.filter(
      (index) => index.Column_name === "gmbLocationId" && index.Non_unique === 0
    );

    if (gmbLocationIdConstraints.length === 0) {
      // Step 4: Add back unique constraint on gmbLocationId only
      console.log("4. Adding back unique constraint on gmbLocationId...");
      await pool.query(`
        ALTER TABLE gmb_locations
        ADD CONSTRAINT UK_gmbLocationId
        UNIQUE (gmbLocationId)
      `);
      console.log("   ✓ Added unique constraint: UK_gmbLocationId");
    } else {
      console.log("4. gmbLocationId unique constraint already exists");
    }

    // Step 5: Verify the rollback
    console.log("5. Verifying rollback...");
    const finalIndexes = await pool.query("SHOW INDEX FROM gmb_locations");
    console.log("Final indexes:", finalIndexes);

    console.log("✅ Rollback completed successfully!");
  } catch (error) {
    console.error("❌ Rollback failed:", error);
    throw error;
  }
}

// Export the function for use in other scripts
module.exports = { rollbackConstraints };

// Run the rollback if this file is executed directly
if (require.main === module) {
  rollbackConstraints()
    .then(() => {
      console.log("Rollback script completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Rollback script failed:", error);
      process.exit(1);
    });
}
