const express = require("express");
const router = express.Router();
const validateSchema = require("../middleware/validateRequest");
const { isAuthenticated } = require("../middleware/isAuthenticated");
const { AccountsList } = require("../controllers/accounts.controller");
const {
  welcome,
  authenticate,
  gmbCallback,
  gmbLocations,
} = require("../controllers/gmb.controller");

/**
 * @swagger
 * components:
 *   schemas:
 *     GMBAccount:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The Google My Business account ID
 *         name:
 *           type: string
 *           description: Name of the GMB account
 *         type:
 *           type: string
 *           description: Type of the account
 *         role:
 *           type: string
 *           description: User's role in the account
 *         accountName:
 *           type: string
 *           description: Display name of the account
 */

/**
 * @swagger
 * /v1/gmb:
 *   get:
 *     summary: GMB welcome page
 *     description: Returns a welcome message for the Google My Business API
 *     tags: [GMB]
 *     responses:
 *       200:
 *         description: Welcome message
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: GMB Home Page
 */
router.get("/", welcome);

/**
 * @swagger
 * /v1/gmb/authenticate:
 *   post:
 *     summary: Authenticate with Google My Business
 *     description: Initiates the OAuth flow for Google My Business authentication
 *     tags: [GMB]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - businessEmail
 *               - businessId
 *             properties:
 *               businessEmail:
 *                 type: string
 *                 format: email
 *                 description: Email address of the business
 *               businessId:
 *                 type: integer
 *                 description: ID of the business
 *     responses:
 *       200:
 *         description: Authentication URL generated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: api is working
 *                 authorizeUrl:
 *                   type: string
 *                   description: URL to redirect the user for Google authentication
 *       400:
 *         description: Authentication failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/authenticate",
  isAuthenticated,
  validateSchema("gmb", "authenticate"),
  authenticate
);

/**
 * @swagger
 * /v1/gmb/callback:
 *   get:
 *     summary: OAuth callback (GET)
 *     description: Callback endpoint for Google OAuth flow (GET method)
 *     tags: [GMB]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: code
 *         schema:
 *           type: string
 *         description: OAuth authorization code
 *       - in: query
 *         name: state
 *         schema:
 *           type: string
 *         description: State parameter for OAuth flow
 *     responses:
 *       200:
 *         description: Authentication successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       400:
 *         description: Authentication failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/callback", isAuthenticated, gmbCallback);

/**
 * @swagger
 * /v1/gmb/callback:
 *   post:
 *     summary: OAuth callback (POST)
 *     description: Callback endpoint for Google OAuth flow (POST method)
 *     tags: [GMB]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *               - state
 *             properties:
 *               code:
 *                 type: string
 *                 description: OAuth authorization code
 *               state:
 *                 type: string
 *                 description: State parameter for OAuth flow
 *     responses:
 *       200:
 *         description: Authentication successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       400:
 *         description: Authentication failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post("/callback", isAuthenticated, gmbCallback);

/**
 * @swagger
 * /v1/gmb/gmbLocations:
 *   post:
 *     summary: Get GMB locations
 *     description: Retrieves Google My Business locations for an account
 *     tags: [GMB]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - accountId
 *             properties:
 *               accountId:
 *                 type: string
 *                 description: Google My Business account ID
 *     responses:
 *       200:
 *         description: Locations retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                       locationName:
 *                         type: string
 *                       address:
 *                         type: object
 *       400:
 *         description: Failed to retrieve locations
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post("/gmbLocations", isAuthenticated, gmbLocations);

/**
 * @swagger
 * /v1/gmb/gmb-accounts/{userId}:
 *   get:
 *     summary: Get GMB accounts
 *     description: Retrieves Google My Business accounts for a user
 *     tags: [GMB]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the user
 *     responses:
 *       200:
 *         description: Accounts retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Accounts List!
 *                 list:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/GMBAccount'
 *       404:
 *         description: Accounts not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/gmb-accounts/:userId", isAuthenticated, AccountsList);

module.exports = router;
