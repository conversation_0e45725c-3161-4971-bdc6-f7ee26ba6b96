const fs = require('fs');
const path = require('path');

/**
 * <PERSON><PERSON><PERSON> to add logging to all controller files
 * This script will add logger imports and basic logging to controller functions
 */

const controllersDir = path.join(__dirname, '../controllers');
const loggerImport = `const logger = require("../utils/logger");`;

// List of controller files to update
const controllerFiles = [
  'business.controller.js',
  'gmb.controller.js',
  'locations.controller.js',
  'posts.controller.js',
  'QandA.controllers.js',
  'reviews.controller.js',
  'role.controller.js',
  'user.controller.js',
  'accounts.controller.js',
  'locationMetrics.controller.js'
];

/**
 * Add logger import to a controller file if not already present
 */
function addLoggerImport(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Check if logger is already imported
    if (content.includes('require("../utils/logger")') || content.includes("require('../utils/logger')")) {
      console.log(`Logger already imported in ${path.basename(filePath)}`);
      return content;
    }
    
    // Find the first require statement and add logger import after it
    const lines = content.split('\n');
    let insertIndex = 0;
    
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('require(') && !lines[i].includes('//')) {
        insertIndex = i + 1;
        break;
      }
    }
    
    lines.splice(insertIndex, 0, loggerImport);
    content = lines.join('\n');
    
    fs.writeFileSync(filePath, content);
    console.log(`Added logger import to ${path.basename(filePath)}`);
    return content;
  } catch (error) {
    console.error(`Error adding logger import to ${filePath}:`, error.message);
    return null;
  }
}

/**
 * Add basic logging template to controller functions
 */
function addBasicLogging(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(filePath, '.js');
    const controllerName = fileName.replace('.controller', '');
    
    console.log(`Adding basic logging structure to ${fileName}`);
    console.log(`Note: You may need to manually adjust the logging calls for specific functions`);
    console.log(`Controller name detected: ${controllerName}`);
    
    // This is a basic template - manual adjustment will be needed for each controller
    const loggingTemplate = `
// Example logging calls to add to your controller functions:
// logger.logControllerAction('${controllerName}', 'functionName', req.requestId, { /* additional data */ });
// logger.info('Operation description', { requestId: req.requestId, /* other data */ });
// logger.error('Error description', { requestId: req.requestId, error: error.message, stack: error.stack });
`;
    
    // Add the template as a comment at the end of the file
    if (!content.includes('Example logging calls')) {
      content += loggingTemplate;
      fs.writeFileSync(filePath, content);
    }
    
    return content;
  } catch (error) {
    console.error(`Error adding logging template to ${filePath}:`, error.message);
    return null;
  }
}

/**
 * Main function to process all controller files
 */
function processControllers() {
  console.log('Starting to add logging to controller files...\n');
  
  controllerFiles.forEach(fileName => {
    const filePath = path.join(controllersDir, fileName);
    
    if (fs.existsSync(filePath)) {
      console.log(`Processing ${fileName}...`);
      
      // Add logger import
      addLoggerImport(filePath);
      
      // Add basic logging template
      addBasicLogging(filePath);
      
      console.log(`Completed ${fileName}\n`);
    } else {
      console.log(`File not found: ${fileName}\n`);
    }
  });
  
  console.log('Logging setup completed!');
  console.log('\nNext steps:');
  console.log('1. Review each controller file');
  console.log('2. Add specific logging calls to each function');
  console.log('3. Use the logging templates provided as comments');
  console.log('4. Test the application to ensure logging works correctly');
}

// Run the script
if (require.main === module) {
  processControllers();
}

module.exports = {
  addLoggerImport,
  addBasicLogging,
  processControllers
};
