# Logging Implementation Guide

## Overview

This document describes the comprehensive logging system implemented for the GMB Social Backend application. The logging system captures all requests, responses, and application events with structured logging for better monitoring and debugging.

## Features

### ✅ **Implemented Features**

- **Request/Response Logging**: All HTTP requests and responses are logged
- **Controller Action Logging**: All controller functions are tracked
- **Error Logging**: Comprehensive error tracking with stack traces
- **Structured Logging**: JSON-formatted logs for easy parsing
- **Log Levels**: ERROR, WARN, INFO, HTTP, DEBUG
- **Sensitive Data Protection**: Automatic redaction of passwords, tokens, etc.
- **Log Rotation**: Automatic cleanup of old log files
- **Environment-based Configuration**: Different log levels for different environments

## File Structure

```
├── utils/
│   └── logger.js                    # Main logger utility
├── middleware/
│   └── requestLogger.js             # Request/response logging middleware
├── config/
│   └── logging.config.js            # Logging configuration
├── scripts/
│   └── add-logging-to-controllers.js # Script to add logging to controllers
├── logs/                            # Log files directory (auto-created)
│   ├── app-YYYY-MM-DD.log          # Application logs
│   ├── http-YYYY-MM-DD.log         # HTTP request/response logs
│   └── error-YYYY-MM-DD.log        # Error logs
└── docs/
    └── logging-guide.md             # This documentation
```

## Configuration

### Environment Variables

Add these to your `.env.development` file:

```env
# Logging configuration
APP_LOG_LEVEL=INFO          # ERROR, WARN, INFO, HTTP, DEBUG
APP_ENV_NAME=DEVELOPMENT    # DEVELOPMENT, STAGING, PRODUCTION
```

### Log Levels

- **ERROR**: Critical errors that need immediate attention
- **WARN**: Warning messages for potential issues
- **INFO**: General information about application flow
- **HTTP**: HTTP request/response details
- **DEBUG**: Detailed debugging information

## Usage Examples

### Basic Logging in Controllers

```javascript
const logger = require("../utils/logger");

const myControllerFunction = async (req, res) => {
  try {
    // Log controller action
    logger.logControllerAction(
      "controllerName",
      "functionName",
      req.requestId,
      {
        userId: req.user?.id,
        additionalData: "value",
      }
    );

    // Log information
    logger.info("Processing request", {
      requestId: req.requestId,
      userId: req.user?.id,
      action: "specific_action",
    });

    // Your business logic here
    const result = await someOperation();

    // Log success
    logger.info("Operation completed successfully", {
      requestId: req.requestId,
      resultCount: result.length,
    });

    res.status(200).json({ success: true, data: result });
  } catch (error) {
    // Log error
    logger.error("Error in myControllerFunction", {
      requestId: req.requestId,
      userId: req.user?.id,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      message: "Internal server error",
      error: error.message,
    });
  }
};
```

### Database Operation Logging

```javascript
// In your model files
const logger = require("../utils/logger");

static async postLocations(locations) {
  try {
    logger.logDatabase('INSERT', 'gmb_locations', 'requestId', {
      recordCount: locations.length
    });

    // Database operation
    const result = await pool.query(/* your query */);

    logger.info('Database operation completed', {
      operation: 'INSERT',
      table: 'gmb_locations',
      affectedRows: result.affectedRows
    });

    return result;
  } catch (error) {
    logger.error('Database operation failed', {
      operation: 'INSERT',
      table: 'gmb_locations',
      error: error.message
    });
    throw error;
  }
}
```

## Log Format

### Request Log Example

```json
{
  "timestamp": "2024-12-19T10:30:45.123Z",
  "level": "HTTP",
  "message": "Incoming Request",
  "environment": "development",
  "requestId": "550e8400-e29b-41d4-a716-446655440000",
  "method": "POST",
  "url": "/api/v1/auth/login",
  "headers": {
    "content-type": "application/json",
    "authorization": "[REDACTED]"
  },
  "query": {},
  "params": {},
  "body": {
    "email": "<EMAIL>",
    "password": "[REDACTED]"
  },
  "ip": "*************",
  "userAgent": "Mozilla/5.0...",
  "userId": "anonymous"
}
```

### Controller Action Log Example

```json
{
  "timestamp": "2024-12-19T10:30:45.456Z",
  "level": "INFO",
  "message": "Controller Action",
  "environment": "development",
  "requestId": "550e8400-e29b-41d4-a716-446655440000",
  "controller": "auth",
  "action": "login",
  "userId": "anonymous",
  "email": "<EMAIL>"
}
```

### Error Log Example

```json
{
  "timestamp": "2024-12-19T10:30:45.789Z",
  "level": "ERROR",
  "message": "Login error",
  "environment": "development",
  "requestId": "550e8400-e29b-41d4-a716-446655440000",
  "email": "<EMAIL>",
  "error": "Invalid credentials",
  "stack": "Error: Invalid credentials\n    at Auth.fetchByEmail..."
}
```

## Security Features

### Sensitive Data Protection

The logging system automatically redacts sensitive information:

- **Headers**: `authorization`, `authentication-token`, `cookie`, `x-api-key`
- **Body Fields**: `password`, `token`, `secret`, `key`, `accessToken`, `refreshToken`
- **Custom Fields**: Configurable in `config/logging.config.js`

### Example of Data Sanitization

```javascript
// Original data
{
  email: "<EMAIL>",
  password: "secretpassword123",
  token: "abc123xyz"
}

// Logged data
{
  email: "<EMAIL>",
  password: "[REDACTED]",
  token: "[REDACTED]"
}
```

## Log Management

### Automatic Log Rotation

- Logs are organized by date: `app-2024-12-19.log`
- Old logs are automatically cleaned up (default: 30 days)
- Configurable retention period in `config/logging.config.js`

### Log File Types

- **app-YYYY-MM-DD.log**: General application logs
- **http-YYYY-MM-DD.log**: HTTP request/response logs
- **error-YYYY-MM-DD.log**: Error logs only

## Monitoring and Analysis

### Log Analysis Tools

You can use various tools to analyze the logs:

1. **grep/awk** for basic filtering
2. **jq** for JSON log parsing
3. **ELK Stack** (Elasticsearch, Logstash, Kibana) for advanced analysis
4. **Splunk** for enterprise log management

### Common Log Queries

```bash
# Find all errors for a specific request
grep "550e8400-e29b-41d4-a716-446655440000" logs/app-2024-12-19.log | grep ERROR

# Find all login attempts
grep "Login attempt" logs/app-2024-12-19.log

# Find slow requests (if performance logging is enabled)
grep "responseTime" logs/http-2024-12-19.log | jq 'select(.responseTime | tonumber > 5000)'

# Count requests by endpoint
grep "Incoming Request" logs/http-2024-12-19.log | jq -r '.url' | sort | uniq -c
```

## Performance Considerations

### Log Level Impact

- **DEBUG**: High volume, use only in development
- **INFO**: Moderate volume, good for production monitoring
- **WARN/ERROR**: Low volume, always enabled

### File I/O Optimization

- Logs are written asynchronously when possible
- Batch writing for high-volume scenarios
- Configurable buffer sizes

## Troubleshooting

### Common Issues

1. **Logs not appearing**

   - Check `APP_LOG_LEVEL` environment variable
   - Verify log directory permissions
   - Check for console errors

2. **Large log files**

   - Reduce log level in production
   - Implement log rotation
   - Monitor disk space

3. **Performance impact**
   - Avoid DEBUG level in production
   - Use async logging where possible
   - Monitor application performance

### Debug Commands

```bash
# Check current log level
echo $APP_LOG_LEVEL

# View recent logs
tail -f logs/app-$(date +%Y-%m-%d).log

# Check log file sizes
ls -lh logs/

# Count log entries by level
grep -c '"level":"ERROR"' logs/app-$(date +%Y-%m-%d).log
```

## Best Practices

1. **Always include requestId** for request tracing
2. **Log at appropriate levels** (don't overuse DEBUG)
3. **Include relevant context** (userId, action, etc.)
4. **Sanitize sensitive data** before logging
5. **Use structured logging** (JSON format)
6. **Monitor log file sizes** and implement rotation
7. **Test logging** in development before deploying

## Next Steps

1. **Add logging to remaining controllers** using the provided script
2. **Configure log monitoring** tools (ELK, Splunk, etc.)
3. **Set up log alerts** for critical errors
4. **Implement log aggregation** for multiple instances
5. **Create log analysis dashboards** for monitoring
