# GMB Locations Table Constraint Migration Summary

## Overview
This document summarizes the database migration created to modify the `gmb_locations` table constraint from a unique constraint on `gmbLocationId` only to a composite unique constraint on `(gmbAccountId, gmbLocationId)`.

## Problem Statement
The current database schema has a unique constraint on `gmbLocationId` only, which prevents the same location ID from existing across different GMB accounts. This is problematic because:
- Different GMB accounts can legitimately have locations with the same ID
- The business logic requires uniqueness only within the context of a specific account

## Solution
Change the unique constraint to be composite on `(gmbAccountId, gmbLocationId)`, allowing:
- Same `gmbLocationId` to exist for different `gmbAccountId` values
- Preventing duplicate combinations of `(gmbAccountId, gmbLocationId)`

## Files Created

### 1. Migration Scripts
- **`database/migrations/modify_gmb_locations_unique_constraint.sql`**
  - Raw SQL migration script
  - Manual execution option

- **`database/migrations/check_and_modify_constraints.js`**
  - Automated JavaScript migration script
  - Includes safety checks and verification
  - Detects existing constraints automatically

### 2. Rollback Scripts
- **`database/migrations/rollback_constraints.js`**
  - Reverts the migration changes
  - Restores original unique constraint on `gmbLocationId` only

### 3. Execution Scripts
- **`database/run_migration.js`**
  - Wrapper script to execute the migration
  - Includes proper error handling and logging

- **`database/run_rollback.js`**
  - Wrapper script to execute the rollback
  - Safe reversion of changes

### 4. Documentation
- **`database/README.md`**
  - Comprehensive documentation
  - Step-by-step instructions
  - Troubleshooting guide

### 5. Package.json Updates
Added npm scripts for easy execution:
```json
{
  "migrate:gmb-locations": "node database/run_migration.js",
  "rollback:gmb-locations": "node database/run_rollback.js"
}
```

## How to Use

### Running the Migration
```bash
# Option 1: Using npm script (recommended)
npm run migrate:gmb-locations

# Option 2: Direct execution
node database/run_migration.js
```

### Rolling Back (if needed)
```bash
# Option 1: Using npm script (recommended)
npm run rollback:gmb-locations

# Option 2: Direct execution
node database/run_rollback.js
```

## Safety Features

### 1. Automatic Detection
- Scripts automatically detect existing constraint names
- No need to manually specify constraint names

### 2. Verification
- Pre-migration checks of current table structure
- Post-migration verification of changes
- Detailed logging throughout the process

### 3. Rollback Capability
- Complete rollback scripts provided
- Can safely revert changes if needed

### 4. Error Handling
- Comprehensive error handling and reporting
- Graceful failure with detailed error messages

## Expected Changes

### Before Migration
```sql
-- Unique constraint on gmbLocationId only
UNIQUE KEY `UK_gmbLocationId` (`gmbLocationId`)
```

### After Migration
```sql
-- Composite unique constraint
UNIQUE KEY `UK_gmb_locations_account_location` (`gmbAccountId`, `gmbLocationId`)
```

## Impact Assessment

### Database Impact
- ✅ No data loss
- ✅ No table structure changes
- ✅ Only constraint modifications
- ✅ Backward compatible queries

### Application Impact
- ✅ No code changes required
- ✅ Existing functionality preserved
- ✅ Enhanced data integrity
- ✅ Supports multi-account scenarios

## Testing Recommendations

1. **Pre-Migration Testing**
   - Backup the `gmb_locations` table
   - Test on development/staging environment first
   - Verify no duplicate `(gmbAccountId, gmbLocationId)` combinations exist

2. **Post-Migration Testing**
   - Verify constraint changes with `SHOW INDEX FROM gmb_locations`
   - Test inserting duplicate `gmbLocationId` with different `gmbAccountId`
   - Test preventing duplicate `(gmbAccountId, gmbLocationId)` combinations
   - Run application tests to ensure functionality

## Maintenance Window
- Estimated downtime: < 5 minutes for typical table sizes
- Lock duration: Brief table lock during constraint modification
- Recommended: Schedule during low-traffic periods

## Backup Strategy
```sql
-- Create backup before migration
CREATE TABLE gmb_locations_backup AS SELECT * FROM gmb_locations;

-- Restore if needed (after dropping current table)
CREATE TABLE gmb_locations AS SELECT * FROM gmb_locations_backup;
```

## Success Criteria
- ✅ Old unique constraint on `gmbLocationId` removed
- ✅ New composite constraint `UK_gmb_locations_account_location` added
- ✅ Application functionality unchanged
- ✅ Data integrity maintained
- ✅ Multi-account location support enabled
