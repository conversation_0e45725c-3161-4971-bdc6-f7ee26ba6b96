const Auth = require("../models/auth.models")

const welcome = (async (req, res) => {
    res.send({
        message: `Auth Home Page`
    })
});

const login = (async (req, res) => {
    const credentials ={
        email:req.body.email,
        password:req.body.password
    }
    try {
        const result = await Auth.fetchByEmail(credentials);
        if (result.user) {
            return res.status(200).json({message: result.message, result:result.user, token:result.token});
        } else {
            return res.status(400).json({ message: result.message });
        }
    } catch (error) {
        return res.status(500).json({ message: 'Internal server error',error:error });
    }
});

module.exports = {
    welcome,
    login
};