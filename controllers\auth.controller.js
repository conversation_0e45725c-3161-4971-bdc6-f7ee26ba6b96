const Auth = require("../models/auth.models");
const logger = require("../utils/logger");

const welcome = async (req, res) => {
  try {
    logger.logControllerAction("auth", "welcome", req.requestId);

    const response = {
      message: `Auth Home Page`,
    };

    logger.info("Auth welcome endpoint accessed", {
      requestId: req.requestId,
      response: response,
    });

    res.send(response);
  } catch (error) {
    logger.error("Error in auth welcome", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res
      .status(500)
      .json({ message: "Internal server error", error: error.message });
  }
};

const login = async (req, res) => {
  try {
    logger.logControllerAction("auth", "login", req.requestId, {
      email: req.body.email, // Log email but not password
    });

    const credentials = {
      email: req.body.email,
      password: req.body.password,
    };

    logger.info("Login attempt", {
      requestId: req.requestId,
      email: credentials.email,
      ip: req.ip || req.connection.remoteAddress,
    });

    const result = await Auth.fetchByEmail(credentials);

    if (result.user) {
      logger.info("Login successful", {
        requestId: req.requestId,
        userId: result.user.id,
        email: credentials.email,
      });

      return res.status(200).json({
        message: result.message,
        result: result.user,
        token: result.token,
      });
    } else {
      logger.warn("Login failed - invalid credentials", {
        requestId: req.requestId,
        email: credentials.email,
        reason: result.message,
      });

      return res.status(400).json({ message: result.message });
    }
  } catch (error) {
    logger.error("Login error", {
      requestId: req.requestId,
      email: req.body.email,
      error: error.message,
      stack: error.stack,
    });

    return res.status(500).json({
      message: "Internal server error",
      error: error.message,
    });
  }
};

module.exports = {
  welcome,
  login,
};
