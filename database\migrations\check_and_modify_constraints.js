// Load environment variables from .env.development
require("dotenv").config({ path: ".env.development" });
const pool = require("../../config/db");

/**
 * Migration script to modify gmb_locations table constraints
 * Changes unique constraint from gmbLocationId only to composite (gmbAccountId, gmbLocationId)
 */

async function checkAndModifyConstraints() {
  try {
    console.log("Starting migration: Modify gmb_locations unique constraint");

    // Step 1: Check current table structure
    console.log("1. Checking current table structure...");
    const tableStructure = await pool.query("SHOW CREATE TABLE gmb_locations");
    console.log("Current table structure:", tableStructure[0]);

    // Step 2: Check current indexes/constraints
    console.log("2. Checking current indexes...");
    const indexes = await pool.query("SHOW INDEX FROM gmb_locations");
    console.log("Current indexes:", indexes);

    // Step 3: Find unique constraints on gmbLocationId
    console.log("3. Finding unique constraints...");
    const uniqueConstraints = await pool.query(
      `
      SELECT CONSTRAINT_NAME, COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE TABLE_SCHEMA = ?
      AND TABLE_NAME = 'gmb_locations'
      AND COLUMN_NAME = 'gmbLocationId'
    `,
      [process.env.APP_DB_NAME]
    );

    console.log("Unique constraints on gmbLocationId:", uniqueConstraints);

    // Step 4: Drop existing unique constraint on gmbLocationId if it exists
    const gmbLocationIdConstraints = indexes.filter(
      (index) => index.Column_name === "gmbLocationId" && index.Non_unique === 0
    );

    if (gmbLocationIdConstraints.length > 0) {
      for (const constraint of gmbLocationIdConstraints) {
        console.log(
          `4. Dropping existing unique constraint: ${constraint.Key_name}`
        );
        await pool.query(
          `ALTER TABLE gmb_locations DROP INDEX ${constraint.Key_name}`
        );
        console.log(`   ✓ Dropped constraint: ${constraint.Key_name}`);
      }
    } else {
      console.log("4. No existing unique constraint on gmbLocationId found");
    }

    // Step 5: Check if composite constraint already exists
    console.log("5. Checking if composite constraint already exists...");
    const compositeConstraints = await pool.query(
      `
      SELECT CONSTRAINT_NAME, GROUP_CONCAT(COLUMN_NAME ORDER BY ORDINAL_POSITION) as columns
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE TABLE_SCHEMA = ?
      AND TABLE_NAME = 'gmb_locations'
      GROUP BY CONSTRAINT_NAME
      HAVING columns LIKE '%gmbAccountId%' AND columns LIKE '%gmbLocationId%'
    `,
      [process.env.APP_DB_NAME]
    );

    if (compositeConstraints.length > 0) {
      console.log(
        "   Composite constraint already exists:",
        compositeConstraints
      );
      console.log("   Migration may have already been applied");
    } else {
      // Step 6: Add new composite unique constraint
      console.log("6. Adding new composite unique constraint...");
      await pool.query(`
        ALTER TABLE gmb_locations
        ADD CONSTRAINT UK_gmb_locations_account_location
        UNIQUE (gmbAccountId, gmbLocationId)
      `);
      console.log(
        "   ✓ Added composite unique constraint: UK_gmb_locations_account_location"
      );
    }

    // Step 7: Verify the changes
    console.log("7. Verifying changes...");
    const finalIndexes = await pool.query("SHOW INDEX FROM gmb_locations");
    console.log("Final indexes:", finalIndexes);

    console.log("✅ Migration completed successfully!");
  } catch (error) {
    console.error("❌ Migration failed:", error);
    throw error;
  }
}

// Export the function for use in other scripts
module.exports = { checkAndModifyConstraints };

// Run the migration if this file is executed directly
if (require.main === module) {
  checkAndModifyConstraints()
    .then(() => {
      console.log("Migration script completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Migration script failed:", error);
      process.exit(1);
    });
}
