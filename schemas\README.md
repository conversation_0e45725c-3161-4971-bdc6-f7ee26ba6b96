## Schema Validator

Documentation available here [https://json-schema.org/learn/getting-started-step-by-step#intro]

[https://ajv.js.org/json-schema.html]

## Folder Structure

    - schemas
        - controllerName1
            - actionFnName1.json
            - actionFnName2.json
            - actionFnName3.json
        - controllerName2
            - actionFnName1.json
            - actionFnName2.json
            - actionFnName3.json

