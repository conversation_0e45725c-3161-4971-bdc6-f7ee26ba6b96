-- Migration: Modify gmb_locations table unique constraint
-- Description: Change unique constraint from gmbLocationId only to composite (gmbAccountId, gmbLocationId)
-- Date: 2024-12-19

-- First, let's check the current table structure and constraints
-- SHOW CREATE TABLE gmb_locations;
-- SHOW INDEX FROM gmb_locations;

-- Step 1: Drop the existing unique constraint on gmbLocationId
-- Note: Replace 'constraint_name' with the actual constraint name found from SHOW INDEX
-- Common constraint names might be: UK_gmbLocationId, gmbLocationId, or similar

-- If the constraint is named 'UK_gmbLocationId' (adjust as needed):
-- ALTER TABLE gmb_locations DROP INDEX UK_gmbLocationId;

-- If the constraint is named 'gmbLocationId' (adjust as needed):
-- ALTER TABLE gmb_locations DROP INDEX gmbLocationId;

-- If you're unsure of the constraint name, you can find it using:
-- SELECT CONSTRAINT_NAME 
-- FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
-- WHERE TABLE_SCHEMA = 'gmb' 
-- AND TABLE_NAME = 'gmb_locations' 
-- AND CONSTRAINT_TYPE = 'UNIQUE';

-- Step 2: Add the new composite unique constraint
-- This ensures that the combination of gmbAccountId and gmbLocationId is unique
ALTER TABLE gmb_locations 
ADD CONSTRAINT UK_gmb_locations_account_location 
UNIQUE (gmbAccountId, gmbLocationId);

-- Verification queries (run these after the migration to verify):
-- SHOW INDEX FROM gmb_locations;
-- SELECT CONSTRAINT_NAME, CONSTRAINT_TYPE 
-- FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
-- WHERE TABLE_SCHEMA = 'gmb' 
-- AND TABLE_NAME = 'gmb_locations';
