const { OAuth2Client } = require("google-auth-library");
const logger = require("../utils/logger");
const { google } = require("googleapis");
const keys = require("../config/OAuthKey.json");
const gmbToken = require("../models/gmb.models");
const cryptoService = require("../services/crypto.service");
const gmbService = require("../services/gmb.service");
const GMB_ACTIONS = require("../constants/gmb-actions");
// const { getLocations } = require("./locations.controller");

const welcome = async (req, res) => {
  res.send({
    message: `GMB Home Page`,
  });
};

const authenticate = async (req, res) => {
  const oAuth2Client = new OAuth2Client(
    keys.web.client_id,
    keys.web.client_secret,
    keys.web.redirect_uris[0]
  );
  const { businessEmail, businessId } = req.body;
  const encText = await cryptoService.encrypt(
    `${businessEmail}||${businessId}`,
    "authentication"
  );
  const authorizeUrl = oAuth2Client.generateAuthUrl({
    access_type: "offline",
    prompt: "consent",
    scope: [
      "https://www.googleapis.com/auth/userinfo.profile",
      "https://www.googleapis.com/auth/plus.business.manage",
      "https://www.googleapis.com/auth/business.manage",
      "https://www.googleapis.com/auth/userinfo.email",
    ],
    state: encText,
  });
  res.json({ message: "api is working", authorizeUrl });
  return authorizeUrl;
};

const gmbLocations = async (req, res) => {
  try {
    const { accessToken, account_id } = req.body;
    // Get locations data
    const locations_result = await gmbService.reqGMBApi({
      req: accessToken,
      action: GMB_ACTIONS.get_locations,
      reqBodyData: account_id,
    });
    return res.json({ message: "locations", locations: locations_result });
  } catch (error) {
    throw error;
  }
};

const gmbAccounts = async (req, requestedBusiness, tokens) => {
  try {
    // Get accounts data
    const accounts_result = await gmbService.reqGMBApi({
      req,
      action: GMB_ACTIONS.get_accounts,
      reqBodyData: null,
    });
    const rawData = await accounts_result.data;
    const accounts_data = [];
    for (let i = 0; i < rawData.accounts.length; i++) {
      const account_data = {
        account_id: rawData.accounts[i].name.split("/")[1],
        account_Name: rawData.accounts[i].accountName,
        account_Type: rawData.accounts[i].type,
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        userId: req.body.userId,
        businessId: requestedBusiness,
      };
      accounts_data.push(account_data);
    }
    await gmbToken.resetAccountStatus(requestedBusiness);
    await gmbToken.updateGoogleSyncComplete(requestedBusiness, req.body.userId);
    await gmbToken.resetOAuth(req.body.userId);
    if (accounts_data.length > 0) {
      for (const account of accounts_data) {
        await gmbToken.InsertOAuth(account);
        await gmbToken.InsertAccount(account);
      }
    }

    return { status: 200, data: true };
  } catch (error) {
    console.log(error);
    return { status: 409, data: false };
  }
};

const gmbCallback = async (req, res) => {
  const oAuth2Client = new OAuth2Client(
    keys.web.client_id,
    keys.web.client_secret,
    keys.web.redirect_uris[0]
  );
  if (req.url.indexOf("/callback") > -1) {
    try {
      const r = await oAuth2Client.getToken(req.body.code);
      oAuth2Client.setCredentials({
        access_token: r.tokens.access_token,
        refresh_token: r.tokens.refresh_token,
      });
      req["user"] = {};
      req["user"]["gmbToken"] = r.tokens.access_token;
      req["user"]["refreshToken"] = r.tokens.refresh_token;
      const peopleApi = google.people({ version: "v1", auth: oAuth2Client });
      const { data } = await peopleApi.people.get({
        resourceName: "people/me",
        personFields: "emailAddresses",
      });
      const email =
        data.emailAddresses && data.emailAddresses.length > 0
          ? data.emailAddresses[0].value
          : null;

      const decryptText = await cryptoService.decrypt(
        req.body.state,
        "authentication"
      );

      const requestedByEmail = decryptText.split("||")[0];
      const requestedBusiness = decryptText.split("||")[1];
      if (
        requestedByEmail.trim().toLowerCase() !== email.trim().toLowerCase()
      ) {
        return res.status(400).json({ message: "Invalid request" });
      }

      // Fetch accounts
      const acounts_result = await gmbAccounts(
        req,
        requestedBusiness,
        r.tokens
      );
      res
        .status(acounts_result.status)
        .json({ message: "data", data: acounts_result.data });
    } catch (error) {
      console.log(error);
      res.status(400).json({
        message: "Failed Authentication",
        file: "gmb.controller",
        error: error.message,
      });
    }
  } else {
    res.status(400).send({
      message: "Authenticate Fn",
      data: req.body,
    });
  }
};

module.exports = {
  welcome,
  authenticate,
  gmbCallback,
  gmbLocations,
};

// Example logging calls to add to your controller functions:
// logger.logControllerAction('gmb', 'functionName', req.requestId, { /* additional data */ });
// logger.info('Operation description', { requestId: req.requestId, /* other data */ });
// logger.error('Error description', { requestId: req.requestId, error: error.message, stack: error.stack });
