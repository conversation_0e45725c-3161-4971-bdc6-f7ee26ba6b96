const express = require("express");
const router = express.Router();
const validateSchema = require("../middleware/validateRequest");
const { isAuthenticated } = require("../middleware/isAuthenticated");
const { gmbTokenMapping } = require("../middleware/gmbTokenMapping");
const {
  uploadMedia,
  startUpload,
  welcome,
  getGooglePosts,
  deleteGooglePost,
  getGooglePostsOnLocationId,
  uploadImageLocal,
  createPost,
  saveSchedule,
} = require("../controllers/posts.controller");
const multer = require("multer");
const upload = multer({ storage: multer.memoryStorage() });

router.post(
  "/upload_media",
  upload.array("files"),
  isAuthenticated,
  uploadMedia
);
router.post("/start_upload", isAuthenticated, gmbTokenMapping, startUpload);
router.get("/", welcome);
router.get(
  "/get-google-posts-location/:userId",
  isAuthenticated,
  getGooglePostsOnLocationId
);
router.get("/get-google-posts/:userId", isAuthenticated, getGooglePosts);
router.post("/delete-google-posts/:userId", isAuthenticated, deleteGooglePost);

router.post(
  "/upload-image-local",
  upload.array("files"),
  isAuthenticated,
  uploadImageLocal
);

router.post("/create-post/:userId", isAuthenticated, createPost);

router.delete("/delete-post/:userId", isAuthenticated, deleteGooglePost);

router.post("/save-Schedule/:userId", isAuthenticated, saveSchedule);


module.exports = router;
