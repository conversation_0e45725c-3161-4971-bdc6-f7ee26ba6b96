const pool = require("../config/db");
const { GoogleGenerativeAI } = require("@google/generative-ai");
const RoleType = require("../constants/dbConstants");

module.exports = class Reviews {
  static async fetchAll(userId, gmbLocationId, limit, offset) {
    console.log("limit, offset", limit, offset);
    try {
      const userData = await pool.query("SELECT * FROM users WHERE id = ?", [
        userId,
      ]);
      if (userData[0].roleId === 1) {
        const results = await pool.query(
          `SELECT gr.*, gl.gmbLocationName, gl.gmbAccountId FROM gmb_reviews gr 
          JOIN gmb_locations gl ON gl.gmbLocationId = gr.locationId
          WHERE locationId = ? LIMIT 100` /*${offset},${limit}*/,
          [gmbLocationId]
        );
        return results;
      } else if (userData[0].roleId === 2) {
        const results = await pool.query(
          "SELECT DISTINCT gr.*, gl.gmbLocationName, gl.gmbAccountId FROM gmb_reviews gr " +
            "JOIN gmb_locations gl ON gl.gmbLocationId = gr.locationId " +
            "JOIN gmb_accounts a ON gl.gmbAccountId = a.accountId " +
            "JOIN gmb_businesses_master b ON a.businessId = b.id " +
            "JOIN user_business ub ON  ub.businessId = b.id " +
            "WHERE ub.userId = ?  AND gr.locationId = ? ORDER BY gr.id DESC LIMIT 100",
          [userId, gmbLocationId]
        );
        return results;
      } else {
        const results = await pool.query(
          "SELECT DISTINCT gr.*, gl.gmbLocationName, gl.gmbAccountId FROM gmb_reviews gr " +
            "JOIN gmb_locations gl ON gl.gmbLocationId = gr.locationId " +
            "JOIN users_gmb_locations ul ON ul.gmbLocationId = gl.gmbLocationId " +
            "WHERE ul.userId = ?  AND gr.locationId = ? ORDER BY gr.id DESC LIMIT 100",
          [userId, gmbLocationId]
        );
        return results;
      }
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async fetchExtendedReviews(
    userId,
    gmbLocationId,
    limit,
    offset,
    orderBy,
    rating,
    tags,
    searchText
  ) {
    console.log("limit, offset", limit, offset);
    try {
      const userData = await pool.query("SELECT * FROM users WHERE id = ?", [
        userId,
      ]);
      if (userData[0].roleId === RoleType.Admin) {
        var adminQuery = `SELECT gr.*, gl.gmbLocationName, gl.gmbAccountId, CASE gr.StarRating
    WHEN 'ONE' THEN 1
    WHEN 'TWO' THEN 2
    WHEN 'THREE' THEN 3
    WHEN 'FOUR' THEN 4
    WHEN 'FIVE' THEN 5 END AS StarRatingInt FROM gmb_reviews gr 
          JOIN gmb_locations gl ON gl.gmbLocationId = gr.locationId
          WHERE locationId = ? AND IFNULL(gr.review, '') LIKE '%${searchText}%' AND gr.StarRating LIKE '%${
          rating === "ALL" ? "" : rating
        }%' ${
          tags === "ALL" ? "" : "AND gr.reviewTags LIKE '%" + tags + "%'"
        } `;

        adminQuery += ` ORDER BY ${orderBy}`;

        const results = await pool.query(adminQuery, [gmbLocationId]);
        return results;
      } else if (userData[0].roleId === RoleType.Manager) {
        var managerQuery = `SELECT DISTINCT gr.*, gl.gmbLocationName, gl.gmbAccountId , CASE gr.StarRating   WHEN 'ONE' THEN 1    WHEN 'TWO' THEN 2    WHEN 'THREE' THEN 3    WHEN 'FOUR' THEN 4    WHEN 'FIVE' THEN 5 END AS StarRatingInt FROM gmb_reviews gr 
            JOIN gmb_locations gl ON gl.gmbLocationId = gr.locationId 
            JOIN gmb_accounts a ON gl.gmbAccountId = a.accountId 
            JOIN gmb_businesses_master b ON a.businessId = b.id
            JOIN user_business ub ON  ub.businessId = b.id
            WHERE ub.userId = ?  AND gr.locationId = ? AND IFNULL(gr.review, '') LIKE '%${searchText}%' AND gr.StarRating LIKE '%${
          rating === "ALL" ? "" : rating
        }%' ${
          tags === "ALL" ? "" : "AND gr.reviewTags LIKE '%" + tags + "%'"
        } `;

        managerQuery += ` ORDER BY ${orderBy}`;
        const results = await pool.query(managerQuery, [userId, gmbLocationId]);
        return results;
      } else {
        var userQuery = `SELECT DISTINCT gr.*, gl.gmbLocationName, gl.gmbAccountId , CASE gr.StarRating   WHEN 'ONE' THEN 1    WHEN 'TWO' THEN 2    WHEN 'THREE' THEN 3    WHEN 'FOUR' THEN 4    WHEN 'FIVE' THEN 5 END AS StarRatingInt FROM gmb_reviews gr 
            JOIN gmb_locations gl ON gl.gmbLocationId = gr.locationId 
            JOIN users_gmb_locations ul ON ul.gmbLocationId = gl.gmbLocationId 
            WHERE ul.userId = ?  AND gr.locationId = ? AND IFNULL(gr.review, '') LIKE '%${searchText}%' AND gr.StarRating LIKE '%${
          rating === "ALL" ? "" : rating
        }%' ${tags === "ALL" ? "" : "AND gr.reviewTags LIKE '%" + tags + "%'"}`;

        userQuery += ` ORDER BY ${orderBy}`;
        const results = await pool.query(userQuery, [userId, gmbLocationId]);
        return results;
      }
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async postReviews(Reviews) {
    try {
      for (const Review of Reviews) {
        const result = await pool.query(
          `INSERT INTO gmb_reviews (
            locationId, reviewId, reviewerName, reviewerProfilePic, review, 
            starRating, createTime, updateTime, reviewReplyComment, reviewReplyUpdateTime
          ) VALUES (?,?,?,?,?,?,?,?,?,?) 
          ON DUPLICATE KEY UPDATE
          reviewerName = VALUES(reviewerName), 
            reviewerProfilePic = VALUES(reviewerProfilePic),
           review = VALUES(review),
           starRating = VALUES(starRating),
          reviewReplyComment = VALUES(reviewReplyComment), 
            reviewReplyUpdateTime = VALUES(reviewReplyUpdateTime) `,
          [
            Review.locationId,
            Review.reviewId,
            Review.reviewerName,
            Review.reviewerProfilePic,
            Review.review,
            Review.starRating,
            Review.createTime,
            Review.updateTime,
            Review.reviewReplyComment,
            Review.reviewReplyUpdateTime,
          ]
        );
      }
      return;
    } catch (error) {
      console.error("Error inserting reviews: ", error);
      return error;
    }
  }
  static async postReviewsReply(reply) {
    try {
      const result = await pool.query(
        `INSERT INTO gmb_reviews_activity(userId, gmbLocationId, gmbReviewId, gmbUserReply, statusId, updatedAt) VALUES (?,?,?,?,?, CURRENT_TIMSTAMP)
           ON DUPLICATE KEY UPDATE gmbUserReply = VALUES(gmbUserReply)`,
        [
          reply.userId,
          reply.gmbLocationId,
          reply.gmbReviewId,
          reply.gmbUserReply,
          reply.statusId,
        ]
      );
      return;
    } catch (error) {
      console.error("Error inserting reviews: ", error);
      return error;
    }
  }

  static async getReplyFromAI(comment, rating) {
    try {
      // Access your API key as an environment variable
      const genAIKey = process.env.APP_GOOGLE_GENAI_KEY;
      const genAI = new GoogleGenerativeAI(process.env.APP_GOOGLE_GENAI_KEY);
      const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
      const result = await model.generateContent(
        `Reply politely to review ${comment}`
      );
      return await result.response.text();
    } catch (error) {
      console.error("Error inserting reviews: ", error);
      return error;
    }
  }

  static async createReviewTags(tagName, createdBy) {
    try {
      const result = await pool.query(
        `INSERT INTO review_tags(TagName, CreatedBy) VALUES (?,?)`,
        [tagName, createdBy]
      );
      return result;
    } catch (error) {
      console.error("Error inserting reviews: ", error);
      return error;
    }
  }

  static async updateReviewTagsToReviews(tagNames, reviewId) {
    try {
      const result = await pool.query(
        `UPDATE gmb_reviews SET reviewTags = ?, updatedAt=CURRENT_TIMESTAMP WHERE id = ?`,
        [tagNames, reviewId]
      );
      return result;
    } catch (error) {
      console.error("Error inserting reviews: ", error);
      return error;
    }
  }

  static async getAllTags() {
    try {
      const results = await pool.query(
        `SELECT * FROM review_tags WHERE IsActive = 1`
      );
      return results;
    } catch (error) {
      console.error("Error inserting reviews: ", error);
      return error;
    }
  }
};
