const express = require("express");
const router = express.Router();
const validateSchema = require("../middleware/validateRequest");
const { isAuthenticated } = require("../middleware/isAuthenticated");

const {
  refreshLocationMetrics,
  getSearchkeywords,
} = require("../controllers/locationMetrics.controller");
const { gmbTokenMapping } = require("../middleware/gmbTokenMapping");

router.post(
  "/performance-locationMetrics",
  isAuthenticated,
  gmbTokenMapping,
  refreshLocationMetrics
);

router.post(
  "/search-keywords",
  isAuthenticated,
  gmbTokenMapping,
  getSearchkeywords
);

module.exports = router;
