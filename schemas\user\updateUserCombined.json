{"type": "object", "properties": {"userId": {"type": "integer"}, "roleId": {"type": "integer"}, "name": {"type": "string", "minLength": 3}, "mobile": {"type": "string", "pattern": "^(\\+91|91|0)?[6789]\\d{9}$"}, "email": {"type": "string", "pattern": "^[a-zA-Z0-9_.±]+@[a-zA-Z0-9-]+.[a-zA-Z0-9-.]+$"}, "mobileVerified": {"type": "integer"}, "emailVerified": {"type": "integer"}, "statusId": {"type": "integer"}, "password": {"type": "string", "minLength": 3}, "confirmPassword": {"type": "string", "minLength": 3}, "locationId": {"type": "array", "items": {"type": "string"}}}, "required": ["userId", "statusId", "mobile", "roleId"], "allOf": [{"if": {"properties": {"password": {"type": "string"}}, "required": ["password"]}, "then": {"required": ["confirmPassword"]}}]}