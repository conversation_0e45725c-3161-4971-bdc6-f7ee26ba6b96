
const GMBModels = require("../models/gmb.models")
const logger = require("../utils/logger");
const Accounts = require("../models/accounts.models")

const AccountsList = (async (req, res) => {
    const userId = req.params.userId;
    try {
        const result = await Accounts.fetchAccounts(userId)
        return res.status(201).json({message:"accounts fetched!",data:result});
    } catch (error) {
        return res.status(400).json({message:"Invalid request!",error:error});
    }
});

module.exports = {
    AccountsList
};
// Example logging calls to add to your controller functions:
// logger.logControllerAction('accounts', 'functionName', req.requestId, { /* additional data */ });
// logger.info('Operation description', { requestId: req.requestId, /* other data */ });
// logger.error('Error description', { requestId: req.requestId, error: error.message, stack: error.stack });
