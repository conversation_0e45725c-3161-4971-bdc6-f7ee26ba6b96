const Business = require("../models/business.models");
const logger = require("../utils/logger");

const welcome = async (req, res) => {
  res.send({
    message: `Business Home Page`,
  });
};

const addBusiness = async (req, res, next) => {
  console.log("hhi", req.body);
  const userId = req.body.userId;
  const businessName = req.body.businessName;
  const businessEmail = req.body.businessEmail;
  const businessUrl = req.body.businessUrl;
  const statusId = req.body.statusId;
  const createdBy = req.body.createdBy;
  const updatedBy = req.body.updatedBy;

  const newBusiness = {
    userId: userId,
    businessName: businessName,
    businessEmail: businessEmail,
    businessUrl: businessUrl,
    statusId: statusId,
    createdBy: createdBy,
    updatedBy: updatedBy,
  };
  Business.Insert(newBusiness)
    .then((result) => {
      res.status(201).json({ message: "Business Created!", response: result });
    })
    .catch((error) => {
      res.status(404).json({ message: "Business Not Created!", error: error });
    });
};

const businessList = (req, res, next) => {
  const userId = req.params.userId;
  Business.fetchAll(userId)
    .then((response) => {
      res.status(201).json({ message: "Business List!", list: response });
    })
    .catch((error) => {
      res.status(404).json({ message: "Business Not Found!", error: error });
    });
};

const businessListPaginated = (req, res, next) => {
  const pageNo = req.query.pageNo;
  const offset = req.query.offset;
  Business.fetchBusinessPaginated(req.params.userId, pageNo, offset)
    .then((response) => {
      res.status(201).json({ message: "Business List!", ...response });
    })
    .catch((error) => {
      res.status(404).json({ message: "Business Not Found!", error: error });
    });
};

const deleteBusiness = (req, res, next) => {
  const id = req.params.id;
  Business.deleteById(id)
    .then((response) => {
      res.status(201).json({ message: "Business Deleted!", list: response });
    })
    .catch((error) => {
      res.status(404).json({
        message: "Could not perfrom operation due to error!",
        error: error,
      });
    });
};

const updateBusiness = (req, res, next) => {
  const newData = {
    id: req.params.id,
    userId: req.body.userId,
    businessName: req.body.businessName,
    businessEmail: req.body.businessEmail,
    businessUrl: req.body.businessUrl,
    statusId: req.body.statusId,
    createdBy: req.body.createdBy,
    updatedBy: req.body.updatedBy,
  };
  Business.updateById(newData)
    .then((response) => {
      res.status(201).json({ message: "Business Updated!", list: response });
    })
    .catch((error) => {
      res.status(404).json({
        message: "Could not perfrom operation due to error!",
        error: error,
      });
    });
};

const enableDisableBusiness = (req, res, next) => {
  const newData = {
    id: req.params.id,
    isActive: req.body.isActive,
  };
  Business.enableDisableBusiness(newData)
    .then((response) => {
      res.status(201).json({ message: "Business Updated!", list: response });
    })
    .catch((error) => {
      res.status(404).json({
        message: "Could not perfrom operation due to error!",
        error: error,
      });
    });
};

module.exports = {
  welcome,
  addBusiness,
  businessList,
  deleteBusiness,
  updateBusiness,
  enableDisableBusiness,
  businessListPaginated,
};

// Example logging calls to add to your controller functions:
// logger.logControllerAction('business', 'functionName', req.requestId, { /* additional data */ });
// logger.info('Operation description', { requestId: req.requestId, /* other data */ });
// logger.error('Error description', { requestId: req.requestId, error: error.message, stack: error.stack });
