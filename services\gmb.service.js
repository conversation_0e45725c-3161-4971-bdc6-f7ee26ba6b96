const axios = require("axios");
const GMB_ENDPOINTS = require("../constants/gmb-endpoints");
const GMB_ACTIONS = require("../constants/gmb-actions");

module.exports = {
  /**
   * Definition of the request object for invoking the GMB API.
   *
   * @typedef {Object} RequestObject
   * @property {any} req - The request object.
   * @property {string} action - The action to perform.
   * @property {any} reqBodyData - The data to send in the request body.
   */
  /**
   * Definition of the request object for invoking the GMB API.
   *
   * @typedef {Object} RequestObject
   * @property {any} req - The request object.
   * @property {string} action - The action to perform.
   * @property {any} reqBodyData - The data to send in the request body.
   */

  /**
   * Example usage:
   *
   * const requestObj = {
   *   req: ...,
   *   action: '...',
   *   reqBodyData: ...
   * };
   */
  /**
   * Example usage:
   *
   * const requestObj = {
   *   req: ...,
   *   action: '...',
   *   reqBodyData: ...
   * };
   */

  /**
   * Function to invoke the GMB API.
   *
   * @param {RequestObject} requestObj - The object containing request details.
   */
  reqGMBApi: async (requestObj) => {
    switch (requestObj.action) {
      case GMB_ACTIONS.get_accounts:
        const result = await fetchAccounts(requestObj.req.user.gmbToken);
        return result;

      case GMB_ACTIONS.get_locations:
        const locations = await fetchLocations(
          requestObj.req.user.gmbToken,
          requestObj.reqBodyData.accountId,
          requestObj.reqBodyData.refreshToken
        );
        return locations;

      case GMB_ACTIONS.get_location_summary:
        const locationSummary = await fetchLocationSummary(
          requestObj.req.user.gmbToken,
          requestObj.reqBodyData.accountId,
          requestObj.reqBodyData.locationId
        );
        return locationSummary;

      case GMB_ACTIONS.get_reviews:
        const reviews = await fetchReviews(
          requestObj.req.user.gmbToken,
          requestObj.reqBodyData.accountId,
          requestObj.reqBodyData.locationId
        );
        return reviews;

      case GMB_ACTIONS.get_QandA:
        const QandA = await fetchQandA(
          requestObj.req.user.gmbToken,
          requestObj.reqBodyData.locationId
        );
        return QandA;

      case GMB_ACTIONS.put_reviewReply:
        const reviewReply = await putReviewReply(
          requestObj.req.user.gmbToken,
          requestObj.reqBodyData
        );
        return reviewReply;

      case GMB_ACTIONS.upsert_QandAReply:
        const QandAReply = await upsertAnswer(
          requestObj.req.user.gmbToken,
          requestObj.reqBodyData
        );
        return QandAReply;

      case GMB_ACTIONS.get_locationMetrics:
        const locationMetrics = await fetchLocationMetrics(
          requestObj.req.user.gmbToken,
          requestObj.reqBodyData
        );
        return locationMetrics;

      case GMB_ACTIONS.get_searchkeywords:
        const searchkeywords = await getSearchkeywords(
          requestObj.req.user.gmbToken,
          requestObj.reqBodyData
        );
        return searchkeywords;

      case GMB_ACTIONS.start_upload:
        const resourceName = await startUploadtoGoogle(
          requestObj.req.user.gmbToken,
          requestObj.reqBodyData
        );
        return resourceName;

      case GMB_ACTIONS.upload_media:
        const mediaKey = await uploadMediatoGoogle(
          requestObj.req.user.gmbToken,
          requestObj.reqBodyData
        );
        return mediaKey;
      case GMB_ACTIONS.retrive_posts:
        const postsResponse = await retrievePostsFromGoogle(
          requestObj.req.user.gmbToken,
          requestObj.reqBodyData
        );
        return postsResponse;
      case GMB_ACTIONS.delete_post:
        const deletePostResponse = await deletePostsFromGoogle(
          requestObj.req.user.gmbToken,
          requestObj.reqBodyData
        );
        return deletePostResponse;
      case GMB_ACTIONS.upload_server_media:
        const uploadedResponse = await uploadServerMediaToGoogle(
          requestObj.req.user.gmbToken,
          requestObj.reqBodyData
        );
        return uploadedResponse;
      case GMB_ACTIONS.retrieve_account_media:
        // not need
        // const mediaResponse = await retrieveMediaForALocation(
        //   requestObj.req.user.gmbToken,
        //   requestObj.reqBodyData
        // );
        return null;
      case GMB_ACTIONS.create_post:
        const createPostResponse = await createPostInGoogle(
          requestObj.req.user.gmbToken,
          requestObj.reqBodyData
        );
        return createPostResponse;
    }
  },
};

const fetchAccounts = async (token) => {
  const headerData = {
    Authorization: `Bearer ${token}`,
  };
  try {
    const response = await axios.get(GMB_ENDPOINTS.get_accounts, {
      headers: headerData,
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error) {
    return {
      success: false,
      status: error.response ? error.response.status : 500,
      data: error.response ? error.response.data : error.message,
    };
  }
};

const fetchLocations = async (token, accountId, refreshToken) => {
  const headerData = {
    Authorization: `Bearer ${token}`,
  };
  try {
    const response = await axios.get(
      GMB_ENDPOINTS.get_locations(accountId, refreshToken),
      {
        headers: headerData,
      }
    );
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      status: error.response ? error.response.status : 500,
      data: error.response ? error.response.data : error.message,
    };
  }
};

const fetchLocationSummary = async (token, accountId, locationId) => {
  const headerData = {
    Authorization: `Bearer ${token}`,
  };
  try {
    const response = await axios.get(
      GMB_ENDPOINTS.get_location_summary(locationId),
      {
        headers: headerData,
      }
    );

    const mediaResponse = await axios.get(
      GMB_ENDPOINTS.upload_server_media(accountId, locationId),
      {
        headers: headerData,
      }
    );
    return {
      success: true,
      status: response.status,
      data: {
        ...response.data,
        mediaInfo: mediaResponse.data ? mediaResponse.data : [],
      },
      // data: response.data,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      status: error.response ? error.response.status : 500,
      data: error.response ? error.response.data : error.message,
    };
  }
};

const fetchReviews = async (token, accountId, locationId) => {
  const headerData = {
    Authorization: `Bearer ${token}`,
  };
  try {
    const response = await axios.get(
      GMB_ENDPOINTS.get_reviews(accountId, locationId),
      {
        headers: headerData,
      }
    );
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      status: error.response ? error.response.status : 500,
      data: error.response ? error.response.data : error.message,
    };
  }
};

const fetchQandA = async (token, locationId) => {
  const headerData = {
    Authorization: `Bearer ${token}`,
  };
  try {
    const response = await axios.get(GMB_ENDPOINTS.get_QandA(locationId), {
      headers: headerData,
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      status: error.response ? error.response.status : 500,
      data: error.response ? error.response.data : error.message,
    };
  }
};

const fetchLocationMetrics = async (token, reqBody) => {
  const headerData = {
    Authorization: `Bearer ${token}`,
  };
  try {
    const response = await axios.get(
      GMB_ENDPOINTS.get_locationMetrics(
        reqBody.locationId,
        reqBody.startDateRange,
        reqBody.endDateRange
      ),
      {
        headers: headerData,
      }
    );
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      status: error.response ? error.response.status : 500,
      data: error.response ? error.response.data : error.message,
    };
  }
};

const getSearchkeywords = async (token, reqBody) => {
  const headerData = {
    Authorization: `Bearer ${token}`,
  };
  try {
    let endPointURL = GMB_ENDPOINTS.get_searchkeywords(
      reqBody.locationId,
      reqBody.startDateRange,
      reqBody.endDateRange
    );

    if (reqBody.pageToken) {
      endPointURL =
        endPointURL + `&pageToken=${encodeURIComponent(reqBody.pageToken)}`;
    }
    const response = await axios.get(endPointURL, {
      headers: headerData,
    });
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      status: error.response ? error.response.status : 500,
      data: error.response ? error.response.data : error.message,
    };
  }
};

const putReviewReply = async (token, data) => {
  const headerData = {
    Authorization: `Bearer ${token}`,
  };
  try {
    const response = await axios.put(
      GMB_ENDPOINTS.put_reviewReply(
        data.accountId,
        data.locationId,
        data.reviewId
      ),
      {
        comment: data.comment,
      },
      {
        headers: headerData,
      }
    );
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      status: error.response ? error.response.status : 500,
      data: error.response ? error.response.data : error.message,
    };
  }
};

const upsertAnswer = async (token, data) => {
  const headerData = {
    Authorization: `Bearer ${token}`,
  };
  try {
    const response = await axios.post(
      GMB_ENDPOINTS.upsert_QandAReply(data.locationId, data.questionId),
      {
        answer: { text: data.answer },
      },
      {
        headers: headerData,
      }
    );
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      status: error.response ? error.response.status : 500,
      data: error.response ? error.response.data : error.message,
    };
  }
};

const startUploadtoGoogle = async (token, data) => {
  const headerData = {
    Authorization: `Bearer ${token}`,
  };
  try {
    const response = await axios.post(
      GMB_ENDPOINTS.start_upload(data.accountId, data.locationId),
      {},
      {
        headers: headerData,
      }
    );
    // console.log(GMB_ENDPOINTS.start_upload(data.locationId, data.locationId),headerData)

    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      status: error.response ? error.response.status : 500,
      data: error.response ? error.response.data : error.message,
    };
  }
};

const uploadMediatoGoogle = async (token, data) => {
  const headerData = {
    Authorization: `Bearer ${token}`,
    "Content-Type": `application/octet-stream`,
  };
  try {
    const response = await axios.post(
      GMB_ENDPOINTS.upload_media(data.resourceName),
      data.fileData,
      {
        headers: headerData,
      }
    );
    console.log(response);

    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      status: error.response ? error.response.status : 500,
      data: error.response ? error.response.data : error.message,
    };
  }
};

const retrievePostsFromGoogle = async (token, data) => {
  const headerData = {
    Authorization: `Bearer ${token}`,
  };
  try {
    const response = await axios.get(
      GMB_ENDPOINTS.retrieve_posts(data.accountId, data.locationId),
      {
        headers: headerData,
      }
    );
    console.log(response);
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      status: error.response ? error.response.status : 500,
      data: error.response ? error.response.data : error.message,
    };
  }
};

const deletePostsFromGoogle = async (token, data) => {
  const headerData = {
    Authorization: `Bearer ${token}`,
  };
  try {
    const response = await axios.delete(GMB_ENDPOINTS.deletePosts(data.route), {
      headers: headerData,
    });
    console.log(response);
    return response;
  } catch (error) {
    console.log(error);
    return {
      success: false,
      status: error.response ? error.response.status : 500,
      data: error.response ? error.response.data : error.message,
    };
  }
};

const uploadServerMediaToGoogle = async (token, data) => {
  const headerData = {
    Authorization: `Bearer ${token}`,
  };
  try {
    const response = await axios.post(
      GMB_ENDPOINTS.upload_server_media(data.accountId, data.locationId),
      {
        mediaFormat: "PHOTO",
        locationAssociation: {
          category: "ADDITIONAL",
        },
        sourceUrl: data.imageURL,
      },
      {
        headers: headerData,
      }
    );
    console.log(response);
    return response;
  } catch (error) {
    console.log(error);
    return {
      success: false,
      status: error.response ? error.response.status : 500,
      data: error.response ? error.response.data : error.message,
    };
  }
};

const createPostInGoogle = async (token, data) => {
  const headerData = {
    Authorization: `Bearer ${token}`,
  };
  try {
    const response = await axios.post(
      GMB_ENDPOINTS.retrieve_posts(data.accountId, data.locationId),
      data.requestObj,
      {
        headers: headerData,
      }
    );
    console.log(response);
    return response;
  } catch (error) {
    console.log(error);
    return {
      success: false,
      status: error.response ? error.response.status : 500,
      data: error.response ? error.response.data : error.message,
    };
  }
};
