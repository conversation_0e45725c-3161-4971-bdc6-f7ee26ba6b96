// Load environment variables from .env.development
require('dotenv').config({ path: '.env.development' });
const pool = require("../config/db");

/**
 * Test script to verify database connection and check gmb_locations table
 * Usage: node database/test_connection.js
 */

async function testConnection() {
  try {
    console.log("=".repeat(60));
    console.log("DATABASE CONNECTION TEST");
    console.log("=".repeat(60));
    
    // Test basic connection
    console.log("1. Testing database connection...");
    const result = await pool.query("SELECT CURRENT_TIMESTAMP as current_time");
    console.log(`   ✓ Connected to MySQL: ${result[0].current_time}`);
    
    // Check if gmb_locations table exists
    console.log("\n2. Checking if gmb_locations table exists...");
    const tableExists = await pool.query(`
      SELECT COUNT(*) as table_count 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'gmb_locations'
    `, [process.env.APP_DB_NAME]);
    
    if (tableExists[0].table_count > 0) {
      console.log("   ✓ gmb_locations table exists");
      
      // Check table structure
      console.log("\n3. Checking table structure...");
      const tableStructure = await pool.query("DESCRIBE gmb_locations");
      console.log("   Table columns:");
      tableStructure.forEach(column => {
        console.log(`     - ${column.Field} (${column.Type}) ${column.Key ? `[${column.Key}]` : ''}`);
      });
      
      // Check current indexes
      console.log("\n4. Checking current indexes...");
      const indexes = await pool.query("SHOW INDEX FROM gmb_locations");
      console.log("   Current indexes:");
      indexes.forEach(index => {
        if (index.Non_unique === 0) {
          console.log(`     - UNIQUE: ${index.Key_name} on ${index.Column_name}`);
        } else {
          console.log(`     - INDEX: ${index.Key_name} on ${index.Column_name}`);
        }
      });
      
      // Check record count
      console.log("\n5. Checking record count...");
      const recordCount = await pool.query("SELECT COUNT(*) as total_records FROM gmb_locations");
      console.log(`   Total records: ${recordCount[0].total_records}`);
      
      // Check for potential duplicate combinations
      console.log("\n6. Checking for duplicate (gmbAccountId, gmbLocationId) combinations...");
      const duplicates = await pool.query(`
        SELECT gmbAccountId, gmbLocationId, COUNT(*) as count
        FROM gmb_locations 
        GROUP BY gmbAccountId, gmbLocationId 
        HAVING count > 1
      `);
      
      if (duplicates.length > 0) {
        console.log("   ⚠️  Found duplicate combinations:");
        duplicates.forEach(dup => {
          console.log(`     - Account: ${dup.gmbAccountId}, Location: ${dup.gmbLocationId}, Count: ${dup.count}`);
        });
        console.log("   ⚠️  These duplicates must be resolved before running the migration!");
      } else {
        console.log("   ✓ No duplicate (gmbAccountId, gmbLocationId) combinations found");
      }
      
    } else {
      console.log("   ❌ gmb_locations table does not exist");
      console.log("   Please ensure the table is created before running the migration");
    }
    
    console.log("\n" + "=".repeat(60));
    console.log("DATABASE CONNECTION TEST COMPLETED");
    console.log("=".repeat(60));
    
  } catch (error) {
    console.error("\n❌ Database connection test failed:");
    console.error("Error details:", error.message);
    console.error("\nPlease check:");
    console.error("1. .env.development file exists and has correct database configuration");
    console.error("2. Database server is running and accessible");
    console.error("3. Database credentials are correct");
    console.error("4. Database name exists");
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testConnection()
    .then(() => {
      console.log("\nTest completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\nTest failed:", error);
      process.exit(1);
    });
}

module.exports = { testConnection };
