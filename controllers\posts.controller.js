const GMB_ACTIONS = require("../constants/gmb-actions");
const logger = require("../utils/logger");
const { reqGMBApi } = require("../services/gmb.service");
const fs = require("fs");
const path = require("path");
const { OAuth2Client } = require("google-auth-library");
const keys = require("../config/OAuthKey.json");
const { google } = require("googleapis");
const gmbToken = require("../models/gmb.models");
const axios = require("axios");
const Location = require("../models/location.models");
const Auth = require("../models/auth.models");
const os = require("os");
const { v4: uuidv4 } = require("uuid"); // Import UUID
const Posts = require("../models/post.models");

const welcome = async (req, res) => {
  res.send({
    message: `Posts Home Page`,
  });
};

const uploadImageLocal = async (req, res) => {
  const files = req.files;

  if (!files || files.length === 0) {
    return res.status(400).json({ message: "Missing file" });
  }

  const uploadDir = path.join(__dirname, "../uploads"); // Store in ~/uploads/

  // Ensure upload directory exists
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  let uploadedFiles = [];

  for (let index = 0; index < files.length; index++) {
    const file = files[index];
    const fileExtension = path.extname(file.originalname); // Keep the original file extension
    const uniqueFileName = `${uuidv4()}${fileExtension}`;
    const filePath = path.join(uploadDir, uniqueFileName);

    // Save file to local storage
    fs.writeFileSync(filePath, file.buffer);

    uploadedFiles.push({
      originalName: file.originalname,
      fileName: uniqueFileName,
      fileUrl: `${req.protocol}://${req.get("host")}/uploads/${uniqueFileName}`, // URL to access file
      size: file.size,
    });
  }

  return res.status(200).json({
    message: "Files uploaded successfully",
    files: uploadedFiles,
  });
};

const startUpload = async (req, res) => {
  const accountId = req.headers["x-gmb-account-id"];
  const locationId = req.headers["x-gmb-location-id"];
  try {
    const result = await reqGMBApi({
      req,
      action: GMB_ACTIONS.start_upload,
      reqBodyData: { accountId, locationId },
    });
    if (result.success) {
      res.status(200).json({ message: "Media uploaded", data: result });
    } else {
      res
        .status(result.status)
        .json({ message: "Failed to upload data", data: result.data });
    }
    // console.log(req)
  } catch (error) {
    res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
};

const uploadMedia = async (req, res) => {
  const resourceName = req.body.resourceName;
  const files = req.files;
  const userId = req.body.userId;
  const businessGroupId = req.body.businessGroupId;
  const businessId = req.body.businessId;
  const locationId = req.body.locationId;

  if (!files) {
    return res.status(400).json({ message: "Missing file" });
  }

  const oAuthTokens = await Location.getTokensToRetrievePostsOnLocation(
    userId,
    businessGroupId,
    businessId,
    locationId
  );

  if (oAuthTokens && oAuthTokens.length > 0) {
    var responseData = await Auth.authenticateGoogle(oAuthTokens[0]);
    req["user"]["gmbToken"] = responseData.accessToken;
    for (let index = 0; index < files.length; index++) {
      const element = files[index];

      const fileData = element.buffer;

      try {
        const result = await reqGMBApi({
          req,
          action: GMB_ACTIONS.upload_media,
          reqBodyData: { resourceName, fileData },
        });
        if (result.success) {
          res
            .status(200)
            .json({ message: "Media uploaded", data: result.data });
        } else {
          res
            .status(result.status)
            .json({ message: "Failed to upload data", data: result.data });
        }
      } catch (error) {
        res
          .status(500)
          .json({ message: "Internal Server Error", error: error.message });
      }
    }
  } else {
  }
};

const getGooglePostsOnLocationId = async (req, res) => {
  try {
    let postsData = [];
    const userId = req.params.userId;
    const businessGroupId = req.query.businessGroupId;
    const businessId = req.query.businessId;
    const locationId = req.query.locationId;
    const oAuthTokens = await Location.getTokensToRetrievePostsOnLocation(
      userId,
      businessGroupId,
      businessId,
      locationId
    );

    if (oAuthTokens && oAuthTokens.length > 0) {
      const uniqueAccountIds = [
        ...new Set(oAuthTokens.map((item) => item.gmbAccountId)),
      ];

      for (let index = 0; index < uniqueAccountIds.length; index++) {
        const element = uniqueAccountIds[index];
        const filteredLocations = oAuthTokens.filter(
          (x) => x.gmbAccountId === element
        );
        var responseData = await Auth.authenticateGoogle(filteredLocations[0]);

        for (let index = 0; index < filteredLocations.length; index++) {
          const location = filteredLocations[index];
          const accountId = location.gmbAccountId;
          const locationId = location.gmbLocationId;
          req["user"]["gmbToken"] = responseData.accessToken;
          try {
            const result = await reqGMBApi({
              req,
              action: GMB_ACTIONS.retrive_posts,
              reqBodyData: { accountId, locationId },
            });

            if (
              result &&
              result.data &&
              result.data.localPosts &&
              result.data.localPosts.length > 0
            ) {
              postsData = [...postsData, ...result.data.localPosts];
            }
          } catch (error) {}
        }
      }

      res.status(200).json({
        message: "Posts Retrieved",
        data: postsData,
        isSuccess: true,
      });
    } else {
      return res
        .status(400)
        .json({ error: "Google Re-authentication required!" });
    }
  } catch (error) {
    console.error("Error fetching Google Posts:", error);
    return [];
  }
};

const getGooglePosts = async (req, res) => {
  try {
    let posts = [];
    const oAuthTokens = await Location.getTokensToRetrievePosts(
      req.params.userId
    );

    if (oAuthTokens) {
      const uniqueAccountIds = [
        ...new Set(oAuthTokens.map((item) => item.gmbAccountId)),
      ];

      for (let index = 0; index < uniqueAccountIds.length; index++) {
        const element = uniqueAccountIds[index];
        const filteredLocations = oAuthTokens.filter(
          (x) => x.gmbAccountId === element
        );
        var responseData = await Auth.authenticateGoogle(filteredLocations[0]);

        for (let index = 0; index < filteredLocations.length; index++) {
          const location = filteredLocations[index];
          const accountId = location.gmbAccountId;
          const locationId = location.gmbLocationId;
          req["user"]["gmbToken"] = responseData.accessToken;
          try {
            const result = await reqGMBApi({
              req,
              action: GMB_ACTIONS.retrive_posts,
              reqBodyData: { accountId, locationId },
            });

            if (
              result &&
              result.data &&
              result.data.localPosts &&
              result.data.localPosts.length > 0
            ) {
              posts = [...posts, ...result.data.localPosts];
            }
          } catch (error) {}
        }
      }

      res.status(200).json({
        message: "Posts Retrieved",
        data: posts,
        isSuccess: true,
      });
    }
  } catch (error) {
    console.error("Error fetching Google Posts:", error);
    return [];
  }
};

const deleteGooglePost = async (req, res) => {
  try {
    const deleteRoute = req.query.id;
    const accountId = deleteRoute.split("/")[1];
    const locationId = deleteRoute.split("/")[3];
    const oAuthTokens = await Location.getTokensToRetrievePosts(
      req.params.userId,
      accountId
    );
    if (oAuthTokens) {
      var selectedRecord = oAuthTokens.filter(
        (x) => x.gmbAccountId === accountId && x.gmbLocationId === locationId
      );
      if (selectedRecord) {
        var responseData = await Auth.authenticateGoogle(selectedRecord[0]);
        req["user"]["gmbToken"] = responseData.accessToken;
        try {
          const result = await reqGMBApi({
            req,
            action: GMB_ACTIONS.delete_post,
            reqBodyData: { route: req.query.id },
          });
          res.status(200).json({
            message: "Posts Deleted",
            data: result,
            isSuccess: true,
          });
        } catch (error) {}
      }
    }
  } catch (error) {
    console.error("Error fetching Google Posts:", error);
    return [];
  }
};

const createPost = async (req, res) => {
  try {
    const locationInfo = req.body.locationInfo;
    const userId = req.params.userId;
    const businessGroupId = locationInfo.accountId;
    const businessId = locationInfo.businessId;
    const locationId = locationInfo.locationId;
    const createGooglePost = req.body.createGooglePost;
    const oAuthTokens = await Location.getTokensToRetrievePostsOnLocation(
      userId,
      businessGroupId,
      businessId,
      locationId
    );

    if (oAuthTokens) {
      const uniqueAccountIds = [
        ...new Set(oAuthTokens.map((item) => item.gmbAccountId)),
      ];
      const element = uniqueAccountIds[0];

      const filteredLocations = oAuthTokens.filter(
        (x) => x.gmbAccountId === element
      );

      const location = filteredLocations[0];
      const gmbAccountId = location.gmbAccountId;
      const gmbLocationId = location.gmbLocationId;
      var responseData = await Auth.authenticateGoogle(filteredLocations[0]);
      req["user"]["gmbToken"] = responseData.accessToken;
      if (
        createGooglePost.topicType === "EVENT" ||
        createGooglePost.topicType === "OFFER"
      ) {
        if (createGooglePost.event && createGooglePost.event.schedule) {
          const startDate = new Date(createGooglePost.event.schedule.startTime);
          const endDate = new Date(createGooglePost.event.schedule.endTime);

          createGooglePost.event.schedule = {
            startDate: {
              year: startDate.getUTCFullYear(),
              month: startDate.getUTCMonth() + 1,
              day: startDate.getUTCDate(),
            },
            startTime: {
              hours: startDate.getUTCHours(),
              minutes: startDate.getUTCMinutes(),
              seconds: startDate.getUTCSeconds(),
              nanos: 0,
            },
            endDate: {
              year: endDate.getUTCFullYear(),
              month: endDate.getUTCMonth() + 1,
              day: endDate.getUTCDate(),
            },
            endTime: {
              hours: endDate.getUTCHours(),
              minutes: endDate.getUTCMinutes(),
              seconds: endDate.getUTCSeconds(),
              nanos: 0,
            },
          };
        }
      }

      const createPostResponse = await reqGMBApi({
        req,
        action: GMB_ACTIONS.create_post,
        reqBodyData: {
          accountId: gmbAccountId,
          locationId: gmbLocationId,
          requestObj: createGooglePost,
        },
      });

      if (createPostResponse.status === 400) {
        res.status(200).json({
          message: "Creation of the post was unsuccessful.",
          data: createPostResponse.data,
          isSuccess: false,
        });
      } else {
        res.status(200).json({
          message: "Post Created",
          data: createPostResponse.data,
          isSuccess: true,
        });
      }
    }
  } catch (error) {
    console.error("Error fetching Google Posts:", error);
    return [];
  }
};

const saveSchedule = async (req, res) => {
  try {
    const locationInfo = req.body.locationInfo;
    const createGooglePost = req.body.createGooglePost;
    result = await Posts.saveSchedules(req.body);
    return res
      .status(201)
      .json({ message: "Schedule Saved Successfully", data: result });
  } catch (error) {
    console.error("Error fetching Google Posts:", error);
    return [];
  }
};

module.exports = {
  welcome,
  uploadMedia,
  startUpload,
  getGooglePosts,
  deleteGooglePost,
  getGooglePostsOnLocationId,
  uploadImageLocal,
  createPost,
  saveSchedule,
};

// Example logging calls to add to your controller functions:
// logger.logControllerAction('posts', 'functionName', req.requestId, { /* additional data */ });
// logger.info('Operation description', { requestId: req.requestId, /* other data */ });
// logger.error('Error description', { requestId: req.requestId, error: error.message, stack: error.stack });
