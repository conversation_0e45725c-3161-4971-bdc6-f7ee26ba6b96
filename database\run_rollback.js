require("dotenv").config({ path: ".env.development" });
const { rollbackConstraints } = require("./migrations/rollback_constraints");

/**
 * <PERSON><PERSON>t to run the database rollback
 * Usage: node database/run_rollback.js
 */

console.log("=".repeat(60));
console.log("GMB LOCATIONS TABLE CONSTRAINT ROLLBACK");
console.log("=".repeat(60));
console.log("This script will rollback the gmb_locations table to:");
console.log(
  "- Remove composite unique constraint on (gmbAccountId, gmbLocationId)"
);
console.log("- Add back unique constraint on gmbLocationId only");
console.log("=".repeat(60));

// Run the rollback
rollbackConstraints()
  .then(() => {
    console.log("\n" + "=".repeat(60));
    console.log("ROLLBACK COMPLETED SUCCESSFULLY!");
    console.log("=".repeat(60));
  })
  .catch((error) => {
    console.error("\n" + "=".repeat(60));
    console.error("ROLLBACK FAILED!");
    console.error("=".repeat(60));
    console.error("Error details:", error);
  });
