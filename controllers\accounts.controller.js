
const GMBModels = require("../models/gmb.models")
const Accounts = require("../models/accounts.models")

const AccountsList = (async (req, res) => {
    const userId = req.params.userId;
    try {
        const result = await Accounts.fetchAccounts(userId)
        return res.status(201).json({message:"accounts fetched!",data:result});
    } catch (error) {
        return res.status(400).json({message:"Invalid request!",error:error});
    }
});

module.exports = {
    AccountsList
};