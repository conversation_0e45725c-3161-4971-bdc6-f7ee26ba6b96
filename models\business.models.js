const pool = require("../config/db");
const RoleType = require("../constants/dbConstants");

module.exports = class Business {
  constructor(
    userId,
    businessName,
    businessEmail,
    businessUrl,
    statusId,
    createdBy,
    updatedBy
  ) {
    this.userId = userId;
    this.businessName = businessName;
    this.businessEmail = businessEmail;
    this.businessUrl = businessUrl;
    this.statusId = statusId;
    this.createdBy = createdBy;
    this.updatedBy = updatedBy;
  }

  static async Insert(business) {
    try {
      const results = await pool.query(
        "INSERT INTO gmb_businesses_master(businessName, businessEmail, businessUrl, statusId, createdBy, updatedBy) VALUES (?, ?, ?, ?, ?, ?)",
        [
          business.businessName,
          business.businessEmail,
          business.businessUrl,
          business.statusId,
          business.createdBy,
          business.updatedBy,
        ]
      );
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async fetchBusinessPaginated(userId, pageNo, offset) {
    try {
      let results;
      let totalRecords;
      const userData = await pool.query("SELECT * FROM users WHERE id = ?", [
        +userId,
      ]);
      if (userData[0].roleId === RoleType.Admin) {
        const rows = await pool.query(
          `SELECT COUNT(*) AS rowsCount
FROM (
    SELECT DISTINCT bm.*, '' AS name, ub.isGoogleSyncComplete
    FROM gmb_businesses_master bm
    LEFT JOIN user_business ub ON ub.businessId = bm.id
    LEFT JOIN users u ON ub.userId = u.id
) AS sub`
        );

        totalRecords = rows[0].rowsCount;
        //
        //  "SELECT bm.*, u.name, ub.isGoogleSyncComplete FROM gmb_businesses_master bm LEFT JOIN user_business ub ON  ub.businessId = bm.id LEFT JOIN users u ON ub.userId = u.id order by bm.businessName LIMIT ? , ?;",
        results = await pool.query(
          "SELECT Distinct bm.*, '' as name ,ub.isGoogleSyncComplete FROM gmb_businesses_master bm LEFT JOIN user_business ub ON  ub.businessId = bm.id LEFT JOIN users u ON ub.userId = u.id order by bm.businessName LIMIT ? , ?;",
          [(+pageNo - 1) * +offset, +offset]
        );
      } else if (userData[0].roleId === RoleType.Manager) {
        const rows = await pool.query(
          "SELECT COUNT(1) as rowsCount FROM gmb_businesses_master bm JOIN user_business ub ON  ub.businessId = bm.id JOIN users u ON ub.userId = u.id WHERE u.id = ?;",
          [userId]
        );

        totalRecords = rows[0].rowsCount;

        results = await pool.query(
          "SELECT bm.*, u.name, ub.isGoogleSyncComplete FROM gmb_businesses_master bm JOIN user_business ub ON  ub.businessId = bm.id JOIN users u ON ub.userId = u.id WHERE u.id = ? order by bm.businessName LIMIT ? , ?;",
          [userId, (+pageNo - 1) * +offset, +offset]
        );
      } else {
        const rows = await pool.query(
          `SELECT COUNT(DISTINCT loc.gmbAccountId) as rowsCount FROM 
    users u JOIN 
    users_gmb_locations ul ON u.id = ul.userId JOIN 
    gmb_locations loc ON ul.gmbLocationId = loc.gmbLocationId JOIN 
    gmb_accounts acc ON loc.gmbAccountId = acc.accountId JOIN 
    gmb_businesses_master gmb ON acc.businessId = gmb.id
    JOIN user_business ub ON  ub.businessId = gmb.id WHERE
    u.id = ?;`,
          [userId]
        );

        totalRecords = rows[0].rowsCount;

        results = await pool.query(
          `SELECT DISTINCT
    u.id AS user_id,
    gmb.*, ub.isGoogleSyncComplete FROM 
    users u JOIN 
    users_gmb_locations ul ON u.id = ul.userId JOIN 
    gmb_locations loc ON ul.gmbLocationId = loc.gmbLocationId JOIN 
    gmb_accounts acc ON loc.gmbAccountId = acc.accountId JOIN 
    gmb_businesses_master gmb ON acc.businessId = gmb.id
    JOIN user_business ub ON  ub.businessId = gmb.id WHERE
    u.id = ? order by gmb.businessName LIMIT ? , ?;`,
          [userId, (+pageNo - 1) * +offset, +offset]
        );
      }

      return {
        pagination: {
          totalRecords: totalRecords,
          pageCount: Math.ceil(totalRecords / offset),
        },
        results: results,
      };
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }

    // try {
    //   const rows = await pool.query(
    //     "SELECT COUNT(1) as rowsCount FROM gmb_businesses_master bm JOIN users u ON bm.userId = u.id;"
    //   );

    //   const totalRecords = rows[0].rowsCount;

    //   if (totalRecords > 0) {
    //     const results = await pool.query(
    //       "SELECT bm.*, u.name FROM gmb_businesses_master bm JOIN users u ON bm.userId = u.id order by bm.businessName LIMIT ? , ?; ",
    //       [(+pageNo - 1) * +offset, +offset]
    //     );

    //     return {
    //       pagination: {
    //         totalRecords: totalRecords,
    //         pageCount: Math.ceil(totalRecords / offset),
    //       },
    //       results: results,
    //     };
    //   } else {
    //     return [];
    //   }
    // } catch (error) {
    //   console.error("Database query failed:", error);
    //   throw error;
    // }
  }

  static async fetchAll(userId) {
    try {
      const userData = await pool.query("SELECT * FROM users WHERE id = ?", [
        userId,
      ]);
      if (userData[0].roleId === RoleType.Admin) {
        const results = await pool.query(
          "SELECT DISTINCT bm.* FROM gmb_businesses_master bm LEFT JOIN user_business ub ON  ub.businessId = bm.id LEFT JOIN users u ON ub.userId = u.id;"
        );
        return results;
      }
      if (userData[0].roleId === RoleType.Manager) {
        const results = await pool.query(
          "SELECT bm.*, u.name, ub.isGoogleSyncComplete FROM gmb_businesses_master bm JOIN user_business ub ON  ub.businessId = bm.id JOIN users u ON ub.userId = u.id where u.id = ?;",
          [userId]
        );
        return results;
      } else {
        const results = await pool.query(
          `SELECT DISTINCT
    u.id AS user_id,
    gmb.*  FROM 
    users u JOIN 
    users_gmb_locations ul ON u.id = ul.userId JOIN 
    gmb_locations loc ON ul.gmbLocationId = loc.gmbLocationId JOIN 
    gmb_accounts acc ON loc.gmbAccountId = acc.accountId JOIN 
    gmb_businesses_master gmb ON acc.businessId = gmb.id WHERE 
    u.id = ?`,
          [userId]
        );
        return results;
      }
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async deleteById(id) {
    try {
      await pool.query(
        "DELETE FROM gmb_locations WHERE gmbAccountId IN (Select accountId FROM gmb_accounts WHERE businessId = ?)",
        [id]
      );
      await pool.query("DELETE FROM gmb_accounts WHERE businessId = ?", [id]);
      await pool.query(
        "DELETE FROM gmb_oauth_tokens WHERE userId IN (Select u.id FROM users u INNER JOIN user_business ub ON ub.UserId = u.id WHERE ub.BusinessId = ?)",
        [id]
      );
      await pool.query("DELETE FROM user_business WHERE businessId = ?", [id]);
      await pool.query(
        "DELETE u FROM users u INNER JOIN user_business ub ON ub.UserId = u.id WHERE ub.BusinessId = ?",
        [id]
      );
      const results = await pool.query(
        "DELETE FROM gmb_businesses_master WHERE id = ?",
        [id]
      );
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async updateById(newData) {
    const id = newData.id;
    const userId = newData.userId;
    const businessName = newData.businessName;
    const businessEmail = newData.businessEmail;
    const businessUrl = newData.businessUrl;
    const statusId = newData.statusId;
    const createdBy = newData.createdBy;
    const updatedBy = newData.updatedBy;
    try {
      const results = await pool.query(
        "UPDATE gmb_businesses_master SET userId = ?, businessName = ?, businessEmail = ?, businessUrl = ?, createdBy = ?, updatedBy = ?, statusId = ? WHERE id = ?",
        [
          null,
          businessName,
          businessEmail,
          businessUrl,
          createdBy,
          updatedBy,
          statusId,
          id,
        ]
      );
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  static async enableDisableBusiness(newData) {
    // Start building the SQL query and parameters array
    let query = "UPDATE gmb_businesses_master SET isActive = ? WHERE id = ?";
    let params = [newData.isActive, newData.id];

    try {
      const results = await pool.query(query, params);
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }
};
