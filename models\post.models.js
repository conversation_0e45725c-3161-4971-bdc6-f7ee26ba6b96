const pool = require("../config/db");
const bcrypt = require("bcryptjs");

module.exports = class Posts {
  static async saveSchedules(requestObj) {
    try {
      const results = await pool.query(
        "INSERT INTO access_token(userId, accessToken) VALUES (?, ?)",
        [requestObj.userId, requestObj.token]
      );
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }
};
