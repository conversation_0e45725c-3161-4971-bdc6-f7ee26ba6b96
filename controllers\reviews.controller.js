const GMB_ACTIONS = require("../constants/gmb-actions");
const logger = require("../utils/logger");
const Reviews = require("../models/reviews.models");
const { reqGMBApi } = require("../services/gmb.service");
const { replyToQuestions } = require("./QandA.controllers");

const welcome = async (req, res) => {
  res.send({
    message: `Reviews Home Page`,
  });
};

const reviewList = (req, res) => {
  const userId = req.params.userId;
  const gmbLocationId = req.headers["x-gmb-location-id"];
  const page = parseInt(req.query.page) || 1;
  const limit =
    parseInt(req.query.limit) > process.env.APP_PAGINATION_MAX_LIMIT
      ? process.env.APP_PAGINATION_MAX_LIMIT
      : parseInt(req.query.limit);
  const offset = (page - 1) * limit;

  Reviews.fetchAll(userId, gmbLocationId, limit, offset)
    .then((response) => {
      res.status(201).json({ message: "Reviews List!", list: response });
    })
    .catch((error) => {
      res.status(404).json({ message: "Reviews Not Found!", error: error });
    });
};

const getExtenderReviewList = (req, res) => {
  const userId = req.params.userId;
  const gmbLocationId = req.headers["x-gmb-location-id"];
  const orderBy = req.body.orderBy || "";
  const rating = req.body.rating || "";
  const tags = req.body.tags || "";
  const searchText = req.body.searchText || "";
  const page = parseInt(req.query.page) || 1;
  const limit =
    parseInt(req.query.limit) > process.env.APP_PAGINATION_MAX_LIMIT
      ? process.env.APP_PAGINATION_MAX_LIMIT
      : parseInt(req.query.limit);
  const offset = (page - 1) * limit;

  Reviews.fetchExtendedReviews(
    userId,
    gmbLocationId,
    limit,
    offset,
    orderBy,
    rating,
    tags,
    searchText
  )
    .then((response) => {
      res.status(201).json({ message: "Reviews List!", list: response });
    })
    .catch((error) => {
      res.status(404).json({ message: "Reviews Not Found!", error: error });
    });
};

const refreshReviews = async (req, res) => {
  const accountId = req.headers["x-gmb-account-id"];
  const locationId = req.headers["x-gmb-location-id"];
  try {
    const result = await reqGMBApi({
      req,
      action: GMB_ACTIONS.get_reviews,
      reqBodyData: { accountId, locationId },
    });
    if (result.success) {
      let reviewsData = [];
      result.data?.reviews?.forEach((review) => {
        reviewsData.push({
          locationId,
          reviewId: review.reviewId,
          reviewerName: review.reviewer.displayName,
          reviewerProfilePic: review.reviewer.profilePhotoUrl,
          review: review.comment,
          starRating: review.starRating,
          createTime: new Date(review.createTime),
          updateTime: new Date(review.updateTime),
          reviewReplyComment: review.reviewReply
            ? review.reviewReply.comment
            : " ",
          reviewReplyUpdateTime: review.reviewReply
            ? review.reviewReply.updateTime
            : new Date(),
        });
      });
      Reviews.postReviews(reviewsData);
      res
        .status(200)
        .json({ success: true, message: "Reviews fetched", data: reviewsData });
    } else {
      res.status(result.status).json({
        success: false,
        message: "Failed to fetch reviews",
        data: result.data,
      });
    }
  } catch (error) {
    res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
};

const replyToReviews = async (req, res) => {
  const { comment, userId } = req.body;
  const accountId = req.headers["x-gmb-account-id"];
  const locationId = req.headers["x-gmb-location-id"];
  const reviewId = req.headers["x-gmb-review-id"];
  try {
    const result = await reqGMBApi({
      req,
      action: GMB_ACTIONS.put_reviewReply,
      reqBodyData: { accountId, locationId, reviewId, comment },
    });
    if (result.data.status === 200) {
      const replyData = {
        userId: userId,
        gmbLocationId: locationId,
        gmbReviewId: reviewId,
        gmbUserReply: comment,
        statusId: 1,
      };
      Reviews.postReviewsReply(replyData);
    }
    res.status(200).json({ message: "Comment Submitted!", data: result });
  } catch (error) {
    console.log(error);
  }
};

const getReplyFromAI = async (req, res) => {
  const { comment, rating } = req.body;
  try {
    const reply = await Reviews.getReplyFromAI(comment, rating);
    res.status(200).json({ message: reply });
  } catch (error) {
    console.log(error);
  }
};

const createReviewTags = async (req, res) => {
  const { tagName, createdBy } = req.body;
  try {
    const reply = await Reviews.createReviewTags(tagName, createdBy);
    res.status(200).json({ message: reply });
  } catch (error) {
    console.log(error);
  }
};

const getAllTags = async (req, res) => {
  try {
    const results = await Reviews.getAllTags();
    res.status(200).json(results);
  } catch (error) {
    console.log(error);
  }
};

const updateReviewTagsToReviews = async (req, res) => {
  const { tagNames, reviewId } = req.body;
  try {
    const reply = await Reviews.updateReviewTagsToReviews(tagNames, reviewId);
    res.status(200).json({ message: reply });
  } catch (error) {
    console.log(error);
  }
};

module.exports = {
  welcome,
  reviewList,
  refreshReviews,
  replyToReviews,
  getReplyFromAI,
  createReviewTags,
  getAllTags,
  updateReviewTagsToReviews,
  getExtenderReviewList,
};

// Example logging calls to add to your controller functions:
// logger.logControllerAction('reviews', 'functionName', req.requestId, { /* additional data */ });
// logger.info('Operation description', { requestId: req.requestId, /* other data */ });
// logger.error('Error description', { requestId: req.requestId, error: error.message, stack: error.stack });
