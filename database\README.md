# Database Migrations

This directory contains database migration scripts for the GMB Social Backend application.

## GMB Locations Table Constraint Migration

### Overview
This migration modifies the `gmb_locations` table to change the unique constraint from `gmbLocationId` only to a composite unique constraint on `(gmbAccountId, gmbLocationId)`.

### Why This Change?
- **Before**: Only `gmbLocationId` was unique, meaning the same location ID couldn't exist multiple times regardless of account
- **After**: The combination of `gmbAccountId` and `gmbLocationId` is unique, allowing the same location ID to exist across different accounts

### Files Structure
```
database/
├── README.md                                    # This documentation
├── run_migration.js                            # Script to run the migration
├── run_rollback.js                             # Script to rollback the migration
└── migrations/
    ├── modify_gmb_locations_unique_constraint.sql  # Raw SQL migration
    ├── check_and_modify_constraints.js             # JavaScript migration script
    └── rollback_constraints.js                     # JavaScript rollback script
```

### Prerequisites
1. Ensure you have a `.env` file with proper database configuration:
   ```
   APP_DB_HOST=your_database_host
   APP_DB_USER=your_database_user
   APP_DB_PASSWORD=your_database_password
   APP_DB_NAME=gmb
   ```

2. Make sure the application is not running or has minimal traffic during migration

### Running the Migration

#### Option 1: Using JavaScript Migration Script (Recommended)
```bash
# Run the migration
node database/run_migration.js
```

This script will:
- Check current table structure and constraints
- Safely remove existing unique constraint on `gmbLocationId`
- Add new composite unique constraint on `(gmbAccountId, gmbLocationId)`
- Verify the changes

#### Option 2: Using Raw SQL
1. Connect to your MySQL database
2. Execute the SQL commands in `migrations/modify_gmb_locations_unique_constraint.sql`
3. Make sure to check the actual constraint names first

### Rolling Back the Migration

If you need to revert the changes:

```bash
# Rollback the migration
node database/run_rollback.js
```

This will:
- Remove the composite unique constraint
- Add back the original unique constraint on `gmbLocationId` only

### Verification

After running the migration, you can verify the changes by:

1. **Check table structure:**
   ```sql
   SHOW CREATE TABLE gmb_locations;
   ```

2. **Check indexes:**
   ```sql
   SHOW INDEX FROM gmb_locations;
   ```

3. **Check constraints:**
   ```sql
   SELECT CONSTRAINT_NAME, CONSTRAINT_TYPE 
   FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
   WHERE TABLE_SCHEMA = 'gmb' 
   AND TABLE_NAME = 'gmb_locations';
   ```

### Expected Results

After successful migration:
- ✅ Composite unique constraint `UK_gmb_locations_account_location` should exist
- ✅ Old unique constraint on `gmbLocationId` only should be removed
- ✅ Same `gmbLocationId` can exist for different `gmbAccountId` values
- ✅ Duplicate combinations of `(gmbAccountId, gmbLocationId)` are prevented

### Troubleshooting

#### Common Issues:

1. **Constraint name not found:**
   - The script automatically detects constraint names
   - If manual SQL is used, check actual constraint names with `SHOW INDEX FROM gmb_locations`

2. **Duplicate data exists:**
   - If there are existing duplicate combinations of `(gmbAccountId, gmbLocationId)`, the migration will fail
   - Clean up duplicate data before running the migration

3. **Permission issues:**
   - Ensure the database user has `ALTER` privileges on the table

#### Data Cleanup (if needed):
If you have duplicate combinations, identify them with:
```sql
SELECT gmbAccountId, gmbLocationId, COUNT(*) as count
FROM gmb_locations 
GROUP BY gmbAccountId, gmbLocationId 
HAVING count > 1;
```

### Impact on Application Code

This migration should not require changes to the application code since:
- The table structure remains the same
- Only constraints are modified
- Existing queries will continue to work
- The application logic already handles `gmbAccountId` and `gmbLocationId` relationships

### Backup Recommendation

Before running the migration:
1. Create a backup of the `gmb_locations` table
2. Test the migration on a development/staging environment first
3. Plan for a maintenance window if this is a production database

```sql
-- Create backup table
CREATE TABLE gmb_locations_backup AS SELECT * FROM gmb_locations;
```
