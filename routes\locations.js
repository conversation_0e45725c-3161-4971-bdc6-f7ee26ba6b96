const express = require("express");
const router = express.Router();
const validateSchema = require("../middleware/validateRequest");
const { isAuthenticated } = require("../middleware/isAuthenticated");
const { gmbTokenMapping } = require("../middleware/gmbTokenMapping");

const {
  welcome,
  refreshLocations,
  locationsList,
  createLocation,
  locationsListPaginated,
  getAccountsCount,
  getLocationSummary,
  getImageBase64,
} = require("../controllers/locations.controller");

/**
 * @swagger
 * components:
 *   schemas:
 *     Location:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: The auto-generated id of the location
 *         userId:
 *           type: integer
 *           description: User ID associated with the location
 *         gmbAccountId:
 *           type: string
 *           description: Google My Business account ID
 *         gmbLocationId:
 *           type: string
 *           description: Google My Business location ID
 *         locationName:
 *           type: string
 *           description: Name of the location
 *         address:
 *           type: string
 *           description: Address of the location
 *         phoneNumber:
 *           type: string
 *           description: Phone number of the location
 *         websiteUrl:
 *           type: string
 *           description: Website URL of the location
 *         latitude:
 *           type: number
 *           format: float
 *           description: Latitude coordinate of the location
 *         longitude:
 *           type: number
 *           format: float
 *           description: Longitude coordinate of the location
 */

/**
 * @swagger
 * /v1/locations/create-location:
 *   post:
 *     summary: Create a new location
 *     description: Creates a new location
 *     tags: [Locations]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - gmbAccountId
 *               - gmbLocationId
 *               - locationName
 *             properties:
 *               userId:
 *                 type: integer
 *               gmbAccountId:
 *                 type: string
 *               gmbLocationId:
 *                 type: string
 *               locationName:
 *                 type: string
 *               address:
 *                 type: string
 *               phoneNumber:
 *                 type: string
 *               websiteUrl:
 *                 type: string
 *               latitude:
 *                 type: number
 *               longitude:
 *                 type: number
 *     responses:
 *       201:
 *         description: Location created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Location Created!
 *                 response:
 *                   type: object
 *       404:
 *         description: Location not created
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post("/create-location", createLocation);

/**
 * @swagger
 * /v1/locations:
 *   get:
 *     summary: Locations welcome page
 *     description: Returns a welcome message for the Locations API
 *     tags: [Locations]
 *     responses:
 *       200:
 *         description: Welcome message
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Locations Home Page
 */
router.get("/", welcome);

/**
 * @swagger
 * /v1/locations/locations-list-refresh/userId/{userId}:
 *   get:
 *     summary: Refresh locations list
 *     description: Refreshes the list of locations from Google My Business for a user
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the user
 *     responses:
 *       200:
 *         description: Locations refreshed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Location'
 *       400:
 *         description: Failed to refresh locations
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/locations-list-refresh/userId/:userId",
  isAuthenticated,
  refreshLocations
);

/**
 * @swagger
 * /v1/locations/locations-list/{userId}:
 *   get:
 *     summary: Get locations list
 *     description: Retrieves the list of locations for a user
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the user
 *     responses:
 *       201:
 *         description: List of locations
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Locations List!
 *                 list:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Location'
 *       404:
 *         description: Locations not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/locations-list/:userId", isAuthenticated, locationsList);

/**
 * @swagger
 * /v1/locations/locations-list/userId/{userId}:
 *   get:
 *     summary: Get paginated locations list
 *     description: Retrieves a paginated list of locations for a user
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the user
 *       - in: query
 *         name: pageNo
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       201:
 *         description: Paginated list of locations
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Locations List!
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Location'
 *                 totalRecords:
 *                   type: integer
 *       404:
 *         description: Locations not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/locations-list/userId/:userId",
  isAuthenticated,
  locationsListPaginated
);

/**
 * @swagger
 * /v1/locations/location-summary:
 *   get:
 *     summary: Get location summary
 *     description: Retrieves a summary of a location from Google My Business
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: header
 *         name: x-gmb-location-id
 *         required: true
 *         schema:
 *           type: string
 *         description: Google My Business location ID
 *     responses:
 *       200:
 *         description: Location summary retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       400:
 *         description: Failed to retrieve location summary
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get(
  "/location-summary",
  isAuthenticated,
  gmbTokenMapping,
  getLocationSummary
);

/**
 * @swagger
 * /v1/locations/image-proxy:
 *   get:
 *     summary: Get image as base64
 *     description: Retrieves an image from a URL and returns it as base64
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: url
 *         required: true
 *         schema:
 *           type: string
 *         description: URL of the image to retrieve
 *     responses:
 *       200:
 *         description: Image retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 base64:
 *                   type: string
 *                   description: Base64-encoded image data
 *       400:
 *         description: Failed to retrieve image
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/image-proxy", isAuthenticated, getImageBase64);

/**
 * @swagger
 * /v1/locations/accounts-count/userId/{userId}:
 *   get:
 *     summary: Get accounts count
 *     description: Retrieves the count of accounts for a user
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the user
 *     responses:
 *       201:
 *         description: Accounts count retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Accounts Count!
 *                 count:
 *                   type: integer
 *       404:
 *         description: Failed to retrieve accounts count
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/accounts-count/userId/:userId", isAuthenticated, getAccountsCount);

module.exports = router;
